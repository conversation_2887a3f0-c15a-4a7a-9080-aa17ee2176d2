import React, { useState, useRef, useEffect } from 'react';
import { startVideoGeneration, getVideoJobStatus, VideoJob } from './services/videoGenerationService';
import './VideoStudio.css';

const VideoStudio: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageName, setImageName] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [jobs, setJobs] = useState<VideoJob[]>([]);
  
  const imageUploadRef = useRef<HTMLInputElement>(null);
  const pollingIntervals = useRef<Map<string, number>>(new Map());

  const triggerImageUpload = () => {
    imageUploadRef.current?.click();
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setImageFile(event.target.files[0]);
      setImageName(event.target.files[0].name);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      alert('Please enter a prompt.');
      return;
    }

    setIsGenerating(true);
    try {
      const initialJob = await startVideoGeneration(prompt, imageFile || undefined);
      
      const newJob: VideoJob = {
        job_id: initialJob.job_id,
        status: 'processing',
        prompt: prompt,
        status_url: initialJob.status_url,
      };
      setJobs(prevJobs => [newJob, ...prevJobs]);

      startPolling(initialJob.job_id);

      // Clear inputs for next generation
      setPrompt('');
      setImageFile(null);
      setImageName('');
      if (imageUploadRef.current) imageUploadRef.current.value = '';

    } catch (error) {
      console.error('Generation failed to start:', error);
      alert((error as Error).message);
    } finally {
      setIsGenerating(false);
    }
  };

  const startPolling = (jobId: string) => {
    const intervalId = window.setInterval(async () => {
      try {
        const updatedJob = await getVideoJobStatus(jobId);
        setJobs(prevJobs => 
          prevJobs.map(job => 
            job.job_id === jobId ? { ...job, ...updatedJob } : job
          )
        );

        if (updatedJob.status === 'completed' || updatedJob.status === 'failed') {
          stopPolling(jobId);
        }
      } catch (error) {
        console.error(`Failed to poll status for job ${jobId}:`, error);
        stopPolling(jobId);
      }
    }, 5000);

    pollingIntervals.current.set(jobId, intervalId);
  };

  const stopPolling = (jobId: string) => {
    const intervalId = pollingIntervals.current.get(jobId);
    if (intervalId) {
      clearInterval(intervalId);
      pollingIntervals.current.delete(jobId);
    }
  };

  useEffect(() => {
    return () => {
      // Cleanup all intervals on unmount
      pollingIntervals.current.forEach(intervalId => clearInterval(intervalId));
    };
  }, []);

  return (
    <div className="video-studio-container">
      <h1 className="title">Video Studio</h1>
      <p className="subtitle">Create stunning 8-second videos with audio from a simple text prompt, with or without a reference image.</p>

      <div className="generator-card shadow-box">
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="prompt-input"
          placeholder="e.g., A majestic lion roaring on a mountain top at sunrise, cinematic quality"
        />
        <div className="controls">
          <button onClick={triggerImageUpload} className="control-btn">
            <i className="fas fa-image"></i> {imageName || 'Attach Image'}
          </button>
          <input
            type="file"
            ref={imageUploadRef}
            onChange={handleImageChange}
            accept="image/*"
            hidden
          />
          <button onClick={handleGenerate} className="generate-btn" disabled={isGenerating}>
            <i className="fas fa-magic"></i> {isGenerating ? 'Generating...' : 'Generate Video'}
          </button>
        </div>
      </div>

      {jobs.length > 0 && (
        <div className="jobs-section">
          {jobs.map((job) => (
            <div key={job.job_id} className="job-card shadow-box">
              <div className="job-header">
                <p className="job-prompt"><strong>Prompt:</strong> {job.prompt}</p>
                <span className={`status-badge ${job.status}`}>{job.status}</span>
              </div>
              <div className="job-content">
                {(job.status === 'processing' || job.status === 'running') && (
                  <div className="spinner-container">
                    <div className="spinner"></div>
                    <p>Your video is being created. This can take a few minutes...</p>
                  </div>
                )}
                {job.status === 'completed' && job.video_url && (
                  <div className="video-container">
                    <video src={`http://localhost:5000${job.video_url}`} controls autoPlay muted loop />
                  </div>
                )}
                {job.status === 'failed' && (
                  <div className="error-container">
                    <p><strong>Error:</strong> {job.error}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default VideoStudio;
