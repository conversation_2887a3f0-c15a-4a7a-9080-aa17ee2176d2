import React, { useState, useEffect } from 'react';
import TextRenderer from './shared/TextRenderer';
import TextControls from './shared/TextControls';
import Slider from './shared/ui/Slider';
import ColorPicker from './shared/ui/ColorPicker';
import { TextDesignConfig } from '../src/types';
import embedFontsIntoSVG from './shared/embedFonts';

// --- Helper Components ---

const Section: React.FC<{ title: string, children: React.ReactNode, defaultOpen?: boolean }> = ({ title, children, defaultOpen = true }) => {
    const [isOpen, setIsOpen] = useState(defaultOpen);
    return (
        <div className="border border-gray-200 rounded-lg">
            <button onClick={() => setIsOpen(!isOpen)} className="w-full text-left font-semibold text-gray-700 p-3 flex justify-between items-center bg-gray-50 hover:bg-gray-100 rounded-t-lg">
                <span>{title}</span>
                <svg className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" /></svg>
            </button>
            {isOpen && <div className="p-4 space-y-4 bg-white">{children}</div>}
        </div>
    );
};



// --- Main Component ---

interface GraphicDesignerWorkspaceProps {
    onGenerate: (svg: string) => void;
    onBack: () => void;
}

type DesignTemplate = 'crest' | 'block' | 'quote';

export const GraphicDesignerWorkspace: React.FC<GraphicDesignerWorkspaceProps> = ({ onGenerate, onBack }) => {
    const [template, setTemplate] = useState<DesignTemplate>('crest');
    
    // Text Content (per-instance configs)
    const [centerConfig, setCenterConfig] = useState<TextDesignConfig>({
      text: 'BRAND',
      fontFamily: 'Bebas Neue',
      fontSize: 120,
      color: '#111111',
      backgroundColor: 'transparent',
      style: 'simple',
      shape: 'none',
      isCircular: false,
      circularRadius: 120,
      circularStartAngle: 0,
      circularDirection: 'clockwise',
    });
    const [topConfig, setTopConfig] = useState<TextDesignConfig>({
      text: 'YOUR',
      fontFamily: 'Bebas Neue',
      fontSize: 40,
      color: '#111111',
      backgroundColor: 'transparent',
      style: 'simple',
      shape: 'none',
      isCircular: true,
      circularRadius: 170,
      circularStartAngle: 0,
      circularDirection: 'clockwise',
    });
    const [bottomConfig, setBottomConfig] = useState<TextDesignConfig>({
      text: 'TAGLINE',
      fontFamily: 'Bebas Neue',
      fontSize: 40,
      color: '#111111',
      backgroundColor: 'transparent',
      style: 'simple',
      shape: 'none',
      isCircular: true,
      circularRadius: 170,
      circularStartAngle: 180,
      circularDirection: 'counterclockwise',
    });
    const [quoteConfig, setQuoteConfig] = useState<TextDesignConfig>({
      text: 'This is a sample quote.',
      fontFamily: 'Bebas Neue',
      fontSize: 40,
      color: '#111111',
      backgroundColor: 'transparent',
      style: 'simple',
      shape: 'none',
      isCircular: false,
      circularRadius: 120,
      circularStartAngle: 0,
      circularDirection: 'clockwise',
    });

    // Styling
    const [fontFamily, setFontFamily] = useState('Bebas Neue');
    const [textColor, setTextColor] = useState('#FFFFFF');
    const [backgroundColor, setBackgroundColor] = useState('transparent');
    const [ringColor, setRingColor] = useState('#D4AF37');
    
    // Effects / layout
    const [isOutlined, setIsOutlined] = useState(false);
    const [hasDoubleRing, setHasDoubleRing] = useState(false);
    const [topTextDistance, setTopTextDistance] = useState(20);
    const [bottomTextDistance, setBottomTextDistance] = useState(20);
    const [topStartOffset, setTopStartOffset] = useState(50);
    const [bottomStartOffset, setBottomStartOffset] = useState(50);

    const fonts = ['Bebas Neue', 'College', 'Varsity', 'Bank Gothic', 'Impact', 'Pacifico'];

    // Crest geometry constants (used by SvgCanvas and auto-fit logic)
    const crestViewBox = {
        width: 500,
        height: 500,
        center: 250,
    };

    const radii = {
        outer: 200,
        middle: 170,
        inner: 140,
    };

    // When switching into the crest template, set an initial sensible center font size
    // so the first selection is approximately centered between rings. This only runs
    // when the user navigates to the crest template and the center font is still the default.
    useEffect(() => {
      if (template !== 'crest') return;
      const mainRing = hasDoubleRing ? radii.outer : radii.middle;
      const secondary = hasDoubleRing ? radii.inner : 0;
      const avail = Math.max(0, mainRing - secondary);
      const initial = Math.max(12, Math.floor(avail * 0.75)); // increase initial center size (50% larger)
      if (centerConfig.fontSize === 120) {
        setCenterConfig({ ...centerConfig, fontSize: initial });
      }
    }, [template, hasDoubleRing]);


    const SvgCanvas = () => {
        // For path text (top/bottom), we'll compute pathD (topPath/bottomPath) and pass into TextRenderer.
        const pathPropsBase = {
            viewBoxSize: 500,
        };

        const crestViewBox = {
            width: 500,
            height: 500,
            center: 250,
        };

        const radii = {
            outer: 200,
            middle: 170,
            inner: 140,
        };

        let mainRingRadius, secondaryRingRadius;
        let topPathRadius = radii.middle + topTextDistance;
        let bottomPathRadius = radii.middle + bottomTextDistance;

        if (hasDoubleRing) {
            mainRingRadius = radii.outer;
            secondaryRingRadius = radii.inner;
        } else {
            mainRingRadius = radii.middle;
            secondaryRingRadius = 0;
        }

        const topPath = `M ${crestViewBox.center - topPathRadius},${crestViewBox.center} A ${topPathRadius},${topPathRadius} 0 0 1 ${crestViewBox.center + topPathRadius},${crestViewBox.center}`;
        const bottomPath = `M ${crestViewBox.center - bottomPathRadius},${crestViewBox.center} A ${bottomPathRadius},${bottomPathRadius} 0 0 0 ${crestViewBox.center + bottomPathRadius},${crestViewBox.center}`;

        const centerProps = {
            fontFamily: centerConfig.fontFamily,
            fontSize: centerConfig.fontSize,
            color: centerConfig.color,
            style: centerConfig.style,
            strokeWidth: centerConfig.strokeWidth ?? (isOutlined ? 4 : 0),
            x: '50%',
            y: '50%',
            anchor: 'middle' as const,
            viewBoxSize: 500,
            backgroundColor: centerConfig.backgroundColor,
            shape: centerConfig.shape,
            rotation: centerConfig.rotation ?? 0,
            image: centerConfig.image,
        };

        return (
            <svg id="svg-canvas-element" width="100%" height="100%" viewBox="0 0 500 500" style={{ backgroundColor }}>
                {template === 'crest' && (
                    <>
                        <defs>
                            <path id="topCirclePath" d={topPath} />
                            <path id="bottomCirclePath" d={bottomPath} />
                        </defs>
                        
                        <circle cx={crestViewBox.center} cy={crestViewBox.center} r={mainRingRadius} fill="none" stroke={ringColor} strokeWidth="10" />
                        {hasDoubleRing && <circle cx={crestViewBox.center} cy={crestViewBox.center} r={secondaryRingRadius} fill="none" stroke={ringColor} strokeWidth="5" />}
                        
                        <TextRenderer id="crest-center" {...centerProps} text={centerConfig.text} />
                        
                        <TextRenderer id="crest-top" fontFamily={topConfig.fontFamily} text={topConfig.text} fontSize={topConfig.fontSize} color={topConfig.color} style={topConfig.style} pathD={topPath} startOffset={topStartOffset} isCircular={true} viewBoxSize={500} backgroundColor={topConfig.backgroundColor} shape={topConfig.shape} rotation={topConfig.rotation ?? 0} image={topConfig.image} />
                        
                        <TextRenderer id="crest-bottom" fontFamily={bottomConfig.fontFamily} text={bottomConfig.text} fontSize={bottomConfig.fontSize} color={bottomConfig.color} style={bottomConfig.style} pathD={bottomPath} startOffset={bottomStartOffset} isCircular={true} viewBoxSize={500} backgroundColor={bottomConfig.backgroundColor} shape={bottomConfig.shape} rotation={bottomConfig.rotation ?? 0} image={bottomConfig.image} />
                    </>
                )}
                        {template === 'block' && (
                            <TextRenderer
                                id="block-center"
                                text={centerConfig.text}
                                fontFamily={centerConfig.fontFamily}
                                fontSize={centerConfig.fontSize}
                                color={centerConfig.color}
                                style={centerConfig.style}
                                x="50%"
                                y="50%"
                                anchor="middle"
                                viewBoxSize={500}
                                backgroundColor={centerConfig.backgroundColor}
                                shape={centerConfig.shape}
                                rotation={centerConfig.rotation ?? 0}
                                image={centerConfig.image}
                            />
                        )}
                        {template === 'quote' && (
                            <TextRenderer
                                id="quote-center"
                                text={quoteConfig.text}
                                fontFamily={quoteConfig.fontFamily}
                                fontSize={quoteConfig.fontSize}
                                color={quoteConfig.color}
                                style={quoteConfig.style}
                                x="50%"
                                y="50%"
                                anchor="middle"
                                viewBoxSize={500}
                                backgroundColor={quoteConfig.backgroundColor}
                                shape={quoteConfig.shape}
                                rotation={quoteConfig.rotation ?? 0}
                                image={quoteConfig.image}
                            />
                        )}
            </svg>
        );
    };

    const handleDownload = async () => {
        const svgElement = document.getElementById('svg-canvas-element') as SVGElement | null;
        if (svgElement) {
            // Collect font families used in the designer to inline (deduped)
            const families = Array.from(new Set([
              centerConfig.fontFamily,
              topConfig.fontFamily,
              bottomConfig.fontFamily,
              quoteConfig.fontFamily,
              fontFamily
            ].filter(Boolean)));
            // Attempt to embed fonts into the SVG. If it fails, fall back to raw serialization.
            try {
              const svgWithFonts = await embedFontsIntoSVG(svgElement, families);
              onGenerate(svgWithFonts);
              return;
            } catch (e) {
              // fallback to plain svg export
            }
            const svgString = new XMLSerializer().serializeToString(svgElement);
            onGenerate(svgString);
        }
    };

    return (
        <div className="w-full min-h-screen bg-gray-50 p-4 md:p-8">
            <div className="max-w-7xl mx-auto">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-3xl font-bold text-gray-900">Classic T-Shirt Logo Designer</h1>
                    <button onClick={onBack} className="px-6 py-2 text-gray-700 bg-white rounded-md shadow-sm hover:bg-gray-100">
                        ← Back to Hub
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8" style={{ alignItems: 'start' }}>
                    {/* Controls */}
                    <div className="lg:col-span-1 bg-white rounded-2xl p-6 shadow-lg space-y-4 max-h-[80vh] overflow-y-auto">
                        <Section title="1. Select Style">
                            <p className="text-sm text-gray-600 mb-3">Choose a template to get started.</p>
                            <div className="grid grid-cols-3 gap-2">
                                <button onClick={() => setTemplate('crest')} className={`p-3 rounded-md text-sm ${template === 'crest' ? 'bg-sky-500 text-white' : 'bg-gray-200'}`}>Crest</button>
                                <button onClick={() => setTemplate('block')} className={`p-3 rounded-md text-sm ${template === 'block' ? 'bg-sky-500 text-white' : 'bg-gray-200'}`}>Block</button>
                                <button onClick={() => setTemplate('quote')} className={`p-3 rounded-md text-sm ${template === 'quote' ? 'bg-sky-500 text-white' : 'bg-gray-200'}`}>Quote</button>
                            </div>
                        </Section>

                        <Section title="2. Edit Text">
                            <div className="space-y-3">
                                {template === 'crest' && (
                          <>
                            <p className="text-sm text-gray-600">A classic circular logo with text on the top, bottom, and center. Use the controls below to edit each text instance.</p>
                            <div className="space-y-3">
                              <div className="mb-3">
                                <h4 className="font-medium text-gray-700 mb-2">Center Text</h4>
                                <TextControls value={centerConfig} onChange={setCenterConfig} />
                              </div>
                              <div className="mb-3">
                                <h4 className="font-medium text-gray-700 mb-2">Top Text</h4>
                                <TextControls value={topConfig} onChange={setTopConfig} />
                              </div>
                              <div className="mb-3">
                                <h4 className="font-medium text-gray-700 mb-2">Bottom Text</h4>
                                <TextControls value={bottomConfig} onChange={setBottomConfig} />
                              </div>
                            </div>
                          </>
                        )}
                        {template === 'block' && (
                          <>
                            <p className="text-sm text-gray-600">A bold, modern logo with oversized central text.</p>
                            <div className="mt-3">
                              <TextControls value={centerConfig} onChange={setCenterConfig} />
                            </div>
                          </>
                        )}
                        {template === 'quote' && (
                          <>
                            <p className="text-sm text-gray-600">A minimalist design for slogans or taglines.</p>
                            <div className="mt-3">
                              <TextControls value={quoteConfig} onChange={setQuoteConfig} />
                            </div>
                          </>
                        )}
                    </div>
                </Section>

                        <Section title="3. Customize">
                            <div className="space-y-4">
                                <select value={fontFamily} onChange={e => setFontFamily(e.target.value)} className="w-full p-2 border rounded">
                                    {fonts.map(f => <option key={f} value={f}>{f}</option>)}
                                </select>
                                {/*
                                  Per-instance text controls now live under "Edit Text".
                                  Removed duplicate center/top/bottom size sliders from "Customize" to avoid conflicting controls.
                                */}
                                {/* Text color and background are managed per-instance in TextControls; duplicates removed. */}
                                {template === 'crest' && <ColorPicker label="Ring Color" color={ringColor} onChange={setRingColor} />}
                                <div className="flex items-center justify-between">
                                    <label className="text-sm">Outline Effect</label>
                                    <input type="checkbox" checked={isOutlined} onChange={e => setIsOutlined(e.target.checked)} />
                                </div>
                                {template === 'crest' && (
                                    <>
                                        <div className="flex items-center justify-between">
                                            <label className="text-sm">Double Ring</label>
                                            <input type="checkbox" checked={hasDoubleRing} onChange={e => setHasDoubleRing(e.target.checked)} />
                                        </div>
                                        <Slider label="Top Text Distance" value={topTextDistance} min={-50} max={50} onChange={setTopTextDistance} />
                                        <Slider label="Top Start Offset" value={topStartOffset} min={0} max={100} onChange={setTopStartOffset} />
                                        <Slider label="Bottom Text Distance" value={bottomTextDistance} min={-50} max={50} onChange={setBottomTextDistance} />
                                        <Slider label="Bottom Start Offset" value={bottomStartOffset} min={0} max={100} onChange={setBottomStartOffset} />
                                    </>
                                )}
                            </div>
                        </Section>
                         <div className="pt-4">
                            <button onClick={handleDownload} className="w-full px-8 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-bold text-lg">
                                Download Graphic
                            </button>
                        </div>
                    </div>

                    {/* SVG Canvas */}
                    <div className="lg:col-span-2 bg-white rounded-2xl p-6 shadow-lg min-h-[60vh]">
                        <SvgCanvas />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default GraphicDesignerWorkspace;
