import React from 'react';

type Props = {
  label: string;
  color: string;
  onChange: (color: string) => void;
};

const ColorPicker: React.FC<Props> = ({ label, color, onChange }) => (
  <div>
    <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
    <div className="flex items-center gap-2 bg-white border border-gray-300 rounded-md p-1">
      <input
        type="color"
        value={color}
        onChange={(e) => onChange(e.target.value)}
        className="w-8 h-8 p-0 border-none bg-transparent cursor-pointer"
      />
      <input
        type="text"
        value={color}
        onChange={(e) => onChange(e.target.value)}
        className="bg-transparent w-full focus:outline-none text-sm"
      />
    </div>
  </div>
);

export default ColorPicker;
