import React from 'react';

type Props = {
  label: string;
  value: number;
  min: number;
  max: number;
  step?: number;
  onChange: (value: number) => void;
  unit?: string;
  disabled?: boolean;
};

const Slider: React.FC<Props> = ({ label, value, min, max, step = 1, onChange, unit = '', disabled = false }) => (
  <div className="space-y-1.5">
    <div className="flex justify-between items-center text-sm font-medium text-gray-700">
      <label className={disabled ? 'text-gray-400' : ''}>{label}</label>
      <span className={disabled ? 'text-gray-400' : 'text-gray-800'}>{value}{unit}</span>
    </div>
    <input
      type="range"
      min={min}
      max={max}
      step={step}
      value={value}
      onChange={(e) => onChange(Number(e.target.value))}
      disabled={disabled}
      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:cursor-not-allowed [&::-webkit-slider-thumb]:bg-sky-500 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full"
    />
  </div>
);

export default Slider;
