import React from 'react';
import { TextDesignConfig } from '../../src/types';

const fonts = [
  { name: 'Bold Impact', value: 'Impact, Arial Black, sans-serif' },
  { name: 'Clean Sans', value: 'Helvetica, Arial, sans-serif' },
  { name: 'Classic Serif', value: 'Georgia, Times, serif' },
  { name: 'Vintage Script', value: 'Brush Script MT, cursive' },
  { name: 'Modern Thin', value: 'Helvetica Neue, Arial, sans-serif' },
  { name: 'Retro Bold', value: 'Arial Black, Gadget, sans-serif' },
];

const styles = [
  { name: 'Simple', value: 'simple' as const },
  { name: 'Outlined', value: 'outlined' as const },
  { name: 'Shadow', value: 'shadow' as const },
  { name: 'Vintage', value: 'vintage' as const },
  { name: 'Modern', value: 'modern' as const },
];

const shapes = [
  { name: 'No Shape', value: 'none' as const },
  { name: 'Circle', value: 'circle' as const },
  { name: 'Rectangle', value: 'rectangle' as const },
  { name: 'Banner', value: 'banner' as const },
];

const Slider: React.FC<{
  label: string; value: number; min: number; max: number; step?: number;
  onChange: (value: number) => void; unit?: string; disabled?: boolean;
}> = ({ label, value, min, max, step = 1, onChange, unit = '', disabled = false }) => (
  <div className="space-y-1.5">
    <div className="flex justify-between items-center text-sm font-medium text-gray-700">
      <label className={disabled ? 'text-gray-400' : ''}>{label}</label>
      <span className={disabled ? 'text-gray-400' : 'text-gray-800'}>{value}{unit}</span>
    </div>
    <input 
      type="range" 
      min={min} 
      max={max} 
      step={step} 
      value={value} 
      onChange={(e) => onChange(Number(e.target.value))} 
      disabled={disabled}
      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:cursor-not-allowed [&::-webkit-slider-thumb]:bg-sky-500 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full"
    />
  </div>
);

type Props = {
  value: TextDesignConfig;
  onChange: (next: TextDesignConfig) => void;
};

export const TextControls: React.FC<Props> = ({ value, onChange }) => {
  const update = (patch: Partial<TextDesignConfig>) => onChange({ ...value, ...patch });

  return (
    <div className="space-y-4">
      <div className="bg-white/50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-3">Edit Text</h3>
        <div className="space-y-3">
          <input
            type="text"
            value={value.text}
            onChange={e => update({ text: e.target.value })}
            className="w-full bg-white/50 border border-gray-900/10 rounded-md p-3"
            placeholder="Enter your text..."
          />
          <Slider label="Font Size" value={value.fontSize} onChange={(v) => update({ fontSize: v })} min={16} max={300} unit="px" />
          <div className="grid grid-cols-2 gap-3">
            <select value={value.fontFamily} onChange={e => update({ fontFamily: e.target.value })} className="w-full bg-white/50 border border-gray-900/10 rounded-md p-2">
              {fonts.map(font => <option key={font.name} value={font.value}>{font.name}</option>)}
            </select>
            <input type="color" value={value.color} onChange={e => update({ color: e.target.value })} className="w-full h-10 p-1 border-none rounded-md" />
          </div>
        </div>
      </div>

      <div className="bg-white/50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-3">Customize</h3>
        <div className="grid grid-cols-2 gap-2 mt-3">
          {styles.map(s => (
            <button key={s.value} type="button" onClick={() => update({ style: s.value })} className={`p-2 rounded-md text-sm ${value.style === s.value ? 'bg-sky-500 text-white' : 'bg-gray-200'}`}>
              {s.name}
            </button>
          ))}
        </div>
      </div>

      <div className="bg-white/50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-3">Circular Text</h3>
        <label className="flex items-center gap-2">
          <input type="checkbox" checked={value.isCircular} onChange={e => update({ isCircular: e.target.checked })} />
          Enable Circular Text
        </label>
        {value.isCircular && (
          <div className="space-y-3 mt-2">
            <Slider label="Radius" value={value.circularRadius} onChange={(v) => update({ circularRadius: v })} min={60} max={200} />
            <Slider label="Angle" value={value.circularStartAngle} onChange={(v) => update({ circularStartAngle: v })} min={0} max={360} />
            <div className="flex gap-2">
              <button type="button" onClick={() => update({ circularDirection: 'clockwise' })} className={`flex-1 p-2 rounded-md text-sm ${value.circularDirection === 'clockwise' ? 'bg-sky-500 text-white' : 'bg-gray-200'}`}>Clockwise</button>
              <button type="button" onClick={() => update({ circularDirection: 'counterclockwise' })} className={`flex-1 p-2 rounded-md text-sm ${value.circularDirection === 'counterclockwise' ? 'bg-sky-500 text-white' : 'bg-gray-200'}`}>Counter-clockwise</button>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white/50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-3">Advanced / Center</h3>

        <div className="space-y-3">
          <Slider label="Rotation (degrees)" value={value.rotation ?? 0} onChange={(v) => update({ rotation: v })} min={-180} max={180} />
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Replace Text with Image</label>
            <input
              type="file"
              accept="image/*,image/svg+xml"
              onChange={(e) => {
                const f = e.target.files && e.target.files[0];
                if (!f) return;
                const reader = new FileReader();
                reader.onload = () => {
                  const result = reader.result as string | null;
                  if (result) update({ image: result });
                };
                reader.readAsDataURL(f);
              }}
            />
            {value.image && (
              <div className="mt-2 flex items-center gap-2">
                <button type="button" onClick={() => update({ image: undefined })} className="px-3 py-1 bg-red-500 text-white rounded-md text-sm">Remove Image</button>
                <span className="text-sm text-gray-600">Image will replace this text on export/preview.</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="bg-white/50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-3">Shape & Background</h3>
        <div className="grid grid-cols-2 gap-2">
          {shapes.map(shapeOption => (
            <button key={shapeOption.value} type="button" onClick={() => update({ shape: shapeOption.value })} className={`p-2 rounded-md text-sm ${value.shape === shapeOption.value ? 'bg-sky-500 text-white' : 'bg-gray-200'}`}>
              {shapeOption.name}
            </button>
          ))}
        </div>
        {value.shape !== 'none' && (
          <div className="mt-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
            <input type="color" value={value.backgroundColor} onChange={e => update({ backgroundColor: e.target.value })} className="w-full h-10 p-1 border-none rounded-md" />
          </div>
        )}
      </div>
    </div>
  );
};

export default TextControls;
