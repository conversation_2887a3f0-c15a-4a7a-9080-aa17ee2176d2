/**
 * embedFonts.ts
 *
 * Runtime helper that fetches woff2 font files from Google Fonts and inlines them
 * into an SVG element as base64 @font-face rules so exported SVGs render with the
 * correct fonts (suitable for POD).
 *
 * Usage:
 *  const svgString = await embedFontsIntoSVG(svgElement, ['<PERSON><PERSON>eue', 'Roboto']);
 *
 * Notes:
 *  - This runs in the browser (uses fetch). Network errors are ignored for robustness.
 *  - Google Fonts CSS is requested from fonts.googleapis.com (CORS should allow this in browsers).
 */

export async function embedFontsIntoSVG(svgElement: SVGElement, families: string[]): Promise<string> {
  if (!svgElement || !families || families.length === 0) {
    return new XMLSerializer().serializeToString(svgElement);
  }

  // Utility: convert ArrayBuffer to base64
  function arrayBufferToBase64(buffer: ArrayBuffer) {
    const bytes = new Uint8Array(buffer);
    const chunkSize = 0x8000;
    let binary = '';
    for (let i = 0; i < bytes.length; i += chunkSize) {
      binary += String.fromCharCode.apply(null, Array.from(bytes.subarray(i, i + chunkSize)));
    }
    return btoa(binary);
  }

  // Normalize family names and dedupe
  const uniqueFamilies = Array.from(new Set(families.map(f => (f || '').trim()).filter(Boolean)));

  const cssFragments: string[] = [];

  for (const family of uniqueFamilies) {
    try {
      // Build Google Fonts CSS URL. Use + for spaces.
      const familyParam = encodeURIComponent(family.replace(/\s+/g, ' ')).replace(/%20/g, '+');
      const cssUrl = `https://fonts.googleapis.com/css2?family=${familyParam}&display=swap`;

      const cssResp = await fetch(cssUrl);
      if (!cssResp.ok) continue;
      const cssText = await cssResp.text();

      // Find woff2 URLs in the CSS
      const urlRegex = /url\((https?:\/\/[^)]+\.woff2[^)]*)\)/g;
      let match;
      const foundUrls: string[] = [];
      while ((match = urlRegex.exec(cssText)) !== null) {
        foundUrls.push(match[1].replace(/["']/g, ''));
      }

      // If no woff2 urls, skip
      if (foundUrls.length === 0) continue;

      // For each found URL, fetch and inline it.
      for (const url of foundUrls) {
        try {
          const fontResp = await fetch(url);
          if (!fontResp.ok) continue;
          const buffer = await fontResp.arrayBuffer();
          const b64 = arrayBufferToBase64(buffer);
          // Minimal @font-face. We don't attempt to parse weight/style from the CSS;
          // this is a practical embedding that will make the font available to the SVG.
          const face = `
@font-face {
  font-family: '${family}';
  src: url('data:font/woff2;base64,${b64}') format('woff2');
  font-display: swap;
}
`;
          cssFragments.push(face);
        } catch (e) {
          // ignore per-font errors
          // console.warn('font fetch failed', e);
        }
      }
    } catch (e) {
      // ignore per-family errors
      // console.warn('failed to fetch css for', family, e);
    }
  }

  // If no CSS fragments were produced, serialize the SVG as-is
  if (cssFragments.length === 0) {
    return new XMLSerializer().serializeToString(svgElement);
  }

  // Insert a <style> element into the svg (namespace aware)
  try {
    const svgNS = 'http://www.w3.org/2000/svg';
    const styleEl = document.createElementNS(svgNS, 'style');
    styleEl.setAttribute('type', 'text/css');
    // Wrap inside CDATA for safety
    styleEl.textContent = `<![CDATA[\n${cssFragments.join('\n')}\n]]>`;
    // Insert as first child so fonts are available for subsequent defs/text
    svgElement.insertBefore(styleEl, svgElement.firstChild);
  } catch (e) {
    // If we can't create an element in the SVG, fallback to prepending a <style> in string form
    const cssText = cssFragments.join('\n');
    const serializer = new XMLSerializer();
    let svgString = serializer.serializeToString(svgElement);
    // Insert into the <svg ...> tag right after the opening tag
    const insertPos = svgString.indexOf('>') + 1;
    const styleString = `<style type="text/css"><![CDATA[\n${cssText}\n]]></style>`;
    svgString = svgString.slice(0, insertPos) + styleString + svgString.slice(insertPos);
    return svgString;
  }

  // Serialize and return
  return new XMLSerializer().serializeToString(svgElement);
}

export default embedFontsIntoSVG;
