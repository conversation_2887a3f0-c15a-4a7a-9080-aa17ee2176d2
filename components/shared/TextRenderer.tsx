import React, { useMemo } from 'react';
import { TextDesignConfig } from '../../src/types';

export type TextRendererProps = {
  id?: string;
  // Core text props (kept similar to TextDesignConfig)
  text: string;
  fontFamily?: string;
  fontSize?: number; // interpreted as px
  color?: string;
  style?: TextDesignConfig['style'];
  shape?: TextDesignConfig['shape'];

  // Circular / path text support
  isCircular?: boolean;
  circularRadius?: number;
  circularStartAngle?: number; // degrees
  circularDirection?: 'clockwise' | 'counterclockwise';
  // A generic path d string (if provided, it will be used instead of auto-generated circular path)
  pathD?: string;
  // startOffset for textPath in percent (0..100)
  startOffset?: number;

  // Positioning for normal (non-path) text
  x?: string | number;
  y?: string | number;
  anchor?: 'start' | 'middle' | 'end';

  // Visual tweaks
  strokeWidth?: number; // used for outlined style
  viewBoxSize?: number; // used if we auto-generate circular paths (center = viewBoxSize/2)

  // Background color for shape rendering
  backgroundColor?: string;

  // Rotation (degrees) and optional image replacement
  rotation?: number;
  image?: string;

  // Allow passing through additional svg props
  className?: string;
};

const defaultProps = {
  fontFamily: 'Helvetica, Arial, sans-serif',
  fontSize: 48,
  color: '#000000',
  style: 'simple' as TextDesignConfig['style'],
  circularRadius: 120,
  circularStartAngle: 0,
  circularDirection: 'clockwise' as 'clockwise' | 'counterclockwise',
  startOffset: 50,
  x: '50%',
  y: '50%',
  anchor: 'middle' as 'middle',
  strokeWidth: 4,
  viewBoxSize: 300,
};

function degToRad(d: number) {
  return (d * Math.PI) / 180;
}

/**
 * Generate a circular path d attribute centered in a square viewBox.
 * center = viewBoxSize / 2
 *
 * startAngle is the angle where the path should begin (degrees).
 * For textPath usage, startOffset controls placement; this path is a full arc.
 */
function generateCircularPath(viewBoxSize: number, radius: number, startAngle = 0, direction: 'clockwise' | 'counterclockwise' = 'clockwise') {
  const center = viewBoxSize / 2;
  // We'll produce a semicircular path (left-to-right) that can be used by textPath with textAnchor="middle".
  // To make this robust, build an arc that goes from left to right across the center at the given radius.
  // Compute start and end points rotated by startAngle.
  const startRad = degToRad(startAngle);
  const endRad = startRad + Math.PI; // half-circle

  const x1 = center + radius * Math.cos(startRad);
  const y1 = center + radius * Math.sin(startRad);

  const x2 = center + radius * Math.cos(endRad);
  const y2 = center + radius * Math.sin(endRad);

  // large-arc-flag = 0 (half circle), sweep-flag depends on direction
  const sweepFlag = direction === 'clockwise' ? 1 : 0;

  // Build path: move to start, arc to end (semi-circle)
  return `M ${x1},${y1} A ${radius},${radius} 0 0 ${sweepFlag} ${x2},${y2}`;
}

const TextRenderer: React.FC<TextRendererProps> = (props) => {
  const p = { ...defaultProps, ...props } as Required<TextRendererProps>;

  // stable id for defs (so multiple renders don't create different ids)
  const uid = useMemo(() => {
    if (p.id) return p.id;
    return `text-${Math.random().toString(36).slice(2, 9)}`;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const pathId = `${uid}-path`;
  const shadowFilterId = `${uid}-shadow`;

  const usePath = Boolean(p.pathD) || p.isCircular;

  // Compute path d if needed
  const computedPathD = useMemo(() => {
    if (p.pathD) return p.pathD;
    if (p.isCircular) {
      // generate centered circular/arc path using viewBoxSize and circularRadius
      return generateCircularPath(p.viewBoxSize, p.circularRadius, p.circularStartAngle, p.circularDirection);
    }
    return '';
  }, [p.pathD, p.isCircular, p.viewBoxSize, p.circularRadius, p.circularStartAngle, p.circularDirection]);

  // helpers for rotation / centering and optional image replacement
  const _center = p.viewBoxSize / 2;
  const _rotation = p.rotation ?? 0;
  const _groupTransform = _rotation ? `rotate(${_rotation} ${_center} ${_center})` : undefined;

  // Map style to svg attributes / patterns
  const getTextElements = () => {
    // Base common text props
    const baseProps: any = {
      fontFamily: p.fontFamily,
      fontSize: p.fontSize,
      fill: 'none',
      stroke: 'none',
      textAnchor: p.anchor === 'middle' ? 'middle' : p.anchor === 'start' ? 'start' : 'end',
      dominantBaseline: 'middle',
    };

    const elements: React.ReactNode[] = [];

    // simple style: filled text
    if (p.style === 'simple' || p.style === 'modern' || p.style === 'vintage') {
      const textProps = {
        ...baseProps,
        fill: p.color,
      };

      if (usePath) {
        elements.push(
          <text id={`${uid}-text`} key="text-path" {...textProps}>
            <textPath href={`#${pathId}`} startOffset={`${p.startOffset}%`} textAnchor={textProps.textAnchor}>
              {p.text}
            </textPath>
          </text>
        );
      } else {
        elements.push(
          <text id={`${uid}-text`} key="text-normal" {...textProps} x={p.x} y={p.y}>
            {p.text}
          </text>
        );
      }
    }

    // outlined style: transparent fill + stroke
    if (p.style === 'outlined') {
      const outlinedProps = {
        ...baseProps,
        fill: 'transparent',
        stroke: p.color,
        strokeWidth: p.strokeWidth,
        paintOrder: 'stroke',
      };

      if (usePath) {
        elements.push(
          <text id={`${uid}-text`} key="outlined-path" {...outlinedProps}>
            <textPath href={`#${pathId}`} startOffset={`${p.startOffset}%`} textAnchor={outlinedProps.textAnchor}>
              {p.text}
            </textPath>
          </text>
        );
      } else {
        elements.push(
          <text id={`${uid}-text`} key="outlined-normal" {...outlinedProps} x={p.x} y={p.y}>
            {p.text}
          </text>
        );
      }
    }

    // shadow style: render shadow (via filter or duplicate) then fill on top
    if (p.style === 'shadow') {
      // shadow element (filtered)
      const shadowProps = {
        ...baseProps,
        fill: p.color,
        filter: `url(#${shadowFilterId})`,
      };

      if (usePath) {
        elements.push(
          <text id={`${uid}-text`} key="shadow-path" {...shadowProps}>
            <textPath href={`#${pathId}`} startOffset={`${p.startOffset}%`} textAnchor={shadowProps.textAnchor}>
              {p.text}
            </textPath>
          </text>
        );
      } else {
        elements.push(
          <text id={`${uid}-text`} key="shadow-normal" {...shadowProps} x={p.x} y={p.y}>
            {p.text}
          </text>
        );
      }
    }

    return elements;
  };

  const content = getTextElements();
  const imgSize = Math.max(16, Math.floor(p.fontSize * 2));

  // shape element (circle / rectangle / banner) rendered behind the text when backgroundColor is provided
  const shapeElement = (() => {
    if (!p.shape || p.shape === 'none' || !p.backgroundColor) return null;
    const size = p.viewBoxSize;
    const cx = _center;
    const cy = _center;
    if (p.shape === 'circle') {
      const r = Math.floor(size * 0.38);
      return <circle cx={cx} cy={cy} r={r} fill={p.backgroundColor} />;
    }
    if (p.shape === 'rectangle') {
      const w = Math.floor(size * 0.8);
      const h = Math.floor(size * 0.28);
      const x = Math.floor((size - w) / 2);
      const y = Math.floor((size - h) / 2);
      return <rect x={x} y={y} width={w} height={h} rx={8} fill={p.backgroundColor} />;
    }
    if (p.shape === 'banner') {
      const w = Math.floor(size * 0.9);
      const h = Math.floor(size * 0.18);
      const x = Math.floor((size - w) / 2);
      const y = Math.floor((size - h) / 2);
      return <rect x={x} y={y} width={w} height={h} rx={6} fill={p.backgroundColor} />;
    }
    return null;
  })();

  return (
    <g className={p.className} transform={_groupTransform}>
      {/* defs for path + shadow filter */}
      <defs>
        {usePath && <path id={pathId} d={computedPathD} fill="none" />}
        {/* basic shadow filter */}
        {p.style === 'shadow' && (
          <filter id={shadowFilterId} x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="2" stdDeviation="2" floodColor="#000" floodOpacity="0.35" />
          </filter>
        )}
      </defs>

      {/* Render background shape first (if any), then either an uploaded image or the text */}
      {shapeElement}
      {p.image ? (
        <image
          href={p.image}
          x={_center - imgSize / 2}
          y={_center - imgSize / 2}
          width={imgSize}
          height={imgSize}
          preserveAspectRatio="xMidYMid meet"
        />
      ) : (
        <>
          {content}
        </>
      )}
    </g>
  );
};

export default TextRenderer;
