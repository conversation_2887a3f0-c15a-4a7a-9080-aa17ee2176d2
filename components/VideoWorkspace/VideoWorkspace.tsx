import React, { useState, useEffect, useRef } from 'react';

interface VideoWorkspaceProps {
  onBack: () => void;
}

const VideoWorkspace: React.FC<VideoWorkspaceProps> = ({ onBack }) => {
  const [prompt, setPrompt] = useState<string>('');
  const [jobId, setJobId] = useState<string | null>(null);
  const [status, setStatus] = useState<string>('idle');
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const intervalRef = useRef<number | null>(null);

  const startVideoGeneration = async () => {
    if (!prompt) {
      setError('Please enter a prompt.');
      return;
    }

    setError(null);
    setStatus('starting');
    setVideoUrl(null);

    try {
      const response = await fetch('http://127.0.0.1:5000/generate-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      if (!response.ok) {
        throw new Error('Failed to start video generation.');
      }

      const data = await response.json();
      setJobId(data.job_id);
      setStatus('processing');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setStatus('failed');
    }
  };

  const checkJobStatus = async () => {
    if (!jobId) return;

    try {
      const response = await fetch(`http://127.0.0.1:5000/video-status/${jobId}`);
      const data = await response.json();

      if (data.status === 'completed') {
        setStatus('completed');
        setVideoUrl(`http://127.0.0.1:5000${data.video_url}`);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      } else if (data.status === 'failed') {
        setStatus('failed');
        setError(data.error || 'An unknown error occurred.');
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    } catch (err) {
      setError('Failed to check job status.');
      setStatus('failed');
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  };

  useEffect(() => {
    if (status === 'processing' && jobId) {
      intervalRef.current = window.setInterval(() => {
        checkJobStatus();
      }, 3000); // Poll every 3 seconds
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status, jobId]);

  return (
    <div className="p-8 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 min-h-screen">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center mb-6">
          <button 
            onClick={onBack}
            className="mr-4 p-2 rounded-full hover:bg-gray-200 transition-colors"
            aria-label="Back to hub"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </button>
          <h1 className="text-4xl font-bold text-gray-800">AI Video Generator</h1>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Enter a prompt to generate a video..."
            className="w-full p-3 border border-gray-300 rounded-lg text-lg focus:ring-2 focus:ring-blue-500"
            rows={4}
          />
          <button
            onClick={startVideoGeneration}
            disabled={status === 'processing' || status === 'starting'}
            className="mt-4 w-full py-3 px-6 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all"
          >
            {status === 'processing' ? 'Generating...' : 'Generate Video'}
          </button>
        </div>

        {(status !== 'idle' || error) && (
          <div className="mt-6 bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold text-gray-700 mb-4">Generation Status</h2>
            {error && <p className="text-red-500">Error: {error}</p>}
            {status === 'starting' && <p>Starting generation...</p>}
            {status === 'processing' && <p>Processing... Please wait.</p>}
            {status === 'completed' && videoUrl && (
              <div>
                <p className="text-green-600 mb-4">Video generation complete!</p>
                <video src={videoUrl} controls autoPlay muted className="w-full rounded-lg" />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoWorkspace;
