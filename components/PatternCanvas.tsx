import React, { forwardRef } from 'react';
import { Background, Layer, ImageLayer, ProceduralLayer, Filters, PatternType } from '../src/types';
import { buildPattern, getPatternMode, type PatternSettings } from '../services/patternEngine';

interface PatternCanvasProps {
  layers?: Layer[];
  background: Background;
  globalRotation: number;
  zoom: number;
  removeBackground?: boolean;
  imageSrc?: string | null;
  patternState?: {
    patternType: PatternType;
    gridCountX: number;
    gridCountY: number;
    spacing: number;
    scale: number;
    rotation: number;
    offset: number;
    alternate: boolean;
    seamless: boolean;
    opacity: number;
    filters: {
      hue: number;
      saturation: number;
      brightness: number;
      contrast: number;
    };
  };
}

const VIEWBOX_SIZE = 1000;

interface PatternCanvasComponentProps extends Omit<PatternCanvasProps, 'layers'> {
  layers?: Layer[];
}

const cssFilters = (f: Partial<Filters>) =>
  [
    `brightness(${f.brightness ?? 100}%)`,
    `contrast(${f.contrast ?? 100}%)`,
    `sepia(${f.sepia ?? 0}%)`,
    `hue-rotate(${f.hueRotate ?? 0}deg)`,
    `invert(${f.invert ?? 0}%)`,
  ].join(' ');

const finishFilter = (layer: ImageLayer, id: string) => {
  const k = layer.filters.finish;
  const sat = 1 + k / 200;
  const con = 1 + k / 200;
  const intercept = -(0.5 * con) + 0.5;
  return (
    <filter id={id}>
      <feColorMatrix type="saturate" values={sat.toString()} result="sat" />
      <feComponentTransfer in="sat" result="con">
        <feFuncR type="linear" slope={con.toString()} intercept={intercept.toString()} />
        <feFuncG type="linear" slope={con.toString()} intercept={intercept.toString()} />
        <feFuncB type="linear" slope={con.toString()} intercept={intercept.toString()} />
      </feComponentTransfer>
    </filter>
  );
};

// Enhanced background removal filter with multiple steps for better results
const backgroundRemovalFilter = (id: string) => (
  <filter id={id}>
    {/* First, convert to grayscale to make color removal easier */}
    <feColorMatrix 
      type="matrix"
      values="0.2126 0.7152 0.0722 0 0
              0.2126 0.7152 0.0722 0 0
              0.2126 0.7152 0.0722 0 0
              0 0 0 1 0"
      result="grayscale"
    />
    
    {/* Apply threshold to separate background from foreground */}
    <feComponentTransfer in="grayscale" result="threshold">
      <feFuncR type="discrete" tableValues="0 1" />
      <feFuncG type="discrete" tableValues="0 1" />
      <feFuncB type="discrete" tableValues="0 1" />
      <feFuncA type="discrete" tableValues="0 1" />
    </feComponentTransfer>
    
    {/* Apply the mask to the original image */}
    <feComposite in="SourceGraphic" in2="threshold" operator="in" result="composite" />
    
    {/* Clean up any remaining artifacts */}
    <feMorphology operator="erode" radius="1" in="composite" result="eroded" />
    <feGaussianBlur stdDeviation="1" in="eroded" result="blur" />
    
    {/* Final composite with original image */}
    <feComposite in="SourceGraphic" in2="blur" operator="in" result="final" />
  </filter>
);

const PatternCanvas = forwardRef<HTMLDivElement, PatternCanvasComponentProps>(({ 
  layers = [], 
  background, 
  globalRotation, 
  zoom,
  removeBackground = false,
  imageSrc,
  patternState
}, ref) => {
    
    const renderBg = () => {
      console.log('🎨 Rendering background:', background);
      
      if (background.type === 'solid') {
        console.log('📦 Rendering solid background:', background.color);
        return (
          <rect
            width={VIEWBOX_SIZE}
            height={VIEWBOX_SIZE}
            fill={background.color}
          />
        );
      }

      if (background.type === 'gradient') {
        console.log('🌈 Rendering gradient background:', background);
        const id = 'bg-grad';
        return (
          <>
            <defs>
              <linearGradient
                id={id}
                gradientTransform={`rotate(${background.angle}, 0.5, 0.5)`}
              >
                <stop offset="0%" stopColor={background.startColor} />
                <stop offset="100%" stopColor={background.endColor} />
              </linearGradient>
            </defs>
            <rect width={VIEWBOX_SIZE} height={VIEWBOX_SIZE} fill={`url(#${id})`} />
          </>
        );
      }

      console.log('🔲 Rendering transparent background (checker pattern)');
      return (
        <>
          <defs>
            <pattern
              id="checker"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <rect width="20" height="20" fill="#e5e7eb" />
              <rect width="20" height="20" x="20" y="20" fill="#e5e7eb" />
              <rect width="20" height="20" x="20" fill="#f3f4f6" />
              <rect width="20" height="20" y="20" fill="#f3f4f6" />
            </pattern>
          </defs>
          <rect width={VIEWBOX_SIZE} height={VIEWBOX_SIZE} fill="url(#checker)" />
        </>
      );
    };

    const renderImageLayer = (layer: ImageLayer) => {
      if (!layer.image) return null;
      
      // Background removal is now handled at the pattern level

      if (layer.mode === 'single') {
        const size = (VIEWBOX_SIZE * layer.scale) / 100;
        const x = (VIEWBOX_SIZE - size) / 2;
        const y = x;
        const filterId = `fin-${layer.id}`;
        
        return (
          <g key={layer.id} opacity={layer.opacity}>
            <defs>
              {finishFilter(layer, filterId)}
            </defs>
            <g
              style={{ filter: cssFilters(layer.filters) }}
              filter={`url(#${filterId})`}
            >
              <image
                href={layer.image}
                x={x}
                y={y}
                width={size}
                height={size}
                transform={`rotate(${layer.rotation}, ${VIEWBOX_SIZE / 2}, ${
                  VIEWBOX_SIZE / 2
                })`}
              />
            </g>
          </g>
        );
      }

      if (layer.mode === 'pattern') {
        // Pattern mode - use the new pattern engine
        const supportedMode = getPatternMode(layer.patternType);
        const settings: PatternSettings = {
          mode: supportedMode,
          densityX: layer.gridCountX,
          densityY: layer.gridCountY,
          scale: layer.scale,
          spacing: layer.spacing,
        };

        try {
          const pattern = buildPattern(layer.image, 512, settings);
          const filterId = `fin-${layer.id}`;

          return (
            <g key={layer.id} opacity={layer.opacity}>
              <defs>
                {finishFilter(layer, filterId)}
                {pattern.defs}
              </defs>
              <g
                style={{ filter: cssFilters(layer.filters) }}
                filter={`url(#${filterId})`}
              >
                <rect
                  width={VIEWBOX_SIZE}
                  height={VIEWBOX_SIZE}
                  fill={pattern.fillUrl}
                />
              </g>
            </g>
          );
        } catch (error) {
          console.warn('Pattern rendering failed, falling back to grid', error);
          // Fallback to grid if pattern rendering fails
          return (
            <g key={layer.id} opacity={layer.opacity}>
              <defs>
                {finishFilter(layer, `fin-${layer.id}`)}
              </defs>
              <g style={{ filter: cssFilters(layer.filters) }}>
                <image
                  href={layer.image}
                  x={0}
                  y={0}
                  width={VIEWBOX_SIZE}
                  height={VIEWBOX_SIZE}
                />
              </g>
            </g>
          );
        }
      }
    };

    const renderProceduralLayer = (layer: ProceduralLayer) => {
      const cfg = layer.config;
      let settings: PatternSettings;

      switch (layer.proceduralType) {
        case 'stripes':
          settings = {
            mode: 'stripes',
            densityX: 1,
            densityY: 1,
            scale: 100,
            spacing: 0,
            stripeWidth: cfg.stripeWidth,
            stripeAngle: cfg.stripeAngle,
            stripeColor: cfg.stripeColor,
          };
          break;

        case 'plaid':
          settings = {
            mode: 'plaid',
            densityX: 1,
            densityY: 1,
            scale: 100,
            spacing: 0,
            plaidWidth: cfg.plaidWidth,
            plaidColor1: cfg.plaidColor1,
            plaidColor2: cfg.plaidColor2,
          };
          break;

        case 'dots': {
          const spacing = Math.max(0, cfg.dotSpacing - cfg.dotSize);
          const tiles = Math.round(VIEWBOX_SIZE / cfg.dotSpacing);
          settings = {
            mode: 'dots',
            densityX: tiles,
            densityY: tiles,
            scale: 100,
            spacing,
            dotDiameter: cfg.dotSize,
            dotColor: cfg.dotColor,
          };
          break;
        }

        default:
          return null;
      }

      try {
        const pattern = buildPattern(null, 100, settings);
        return (
          <g key={layer.id} opacity={layer.opacity}>
            <defs>{pattern.defs}</defs>
            <rect
              width={VIEWBOX_SIZE}
              height={VIEWBOX_SIZE}
              fill={pattern.fillUrl}
            />
          </g>
        );
      } catch (error) {
        console.warn(`Procedural pattern generation failed:`, error);
        return null;
      }
    };

    const renderLayer = (layer: Layer) =>
      layer.type === 'image'
        ? renderImageLayer(layer)
        : renderProceduralLayer(layer);

    // Render the uploaded image with pattern settings if available
    const renderUploadedImage = () => {
      if (!imageSrc) return null;
      
      // If we have patternState, use it to create a pattern
      if (patternState) {
        try {
          // Apply pattern settings with all available options
          const settings: PatternSettings = {
            mode: getPatternMode(patternState.patternType),
            densityX: patternState.gridCountX,
            densityY: patternState.gridCountY,
            scale: patternState.scale,
            spacing: patternState.spacing,
            stripeWidth: 40, // Default value, can be customized
            stripeAngle: patternState.rotation,
            stripeColor: '#000000', // Default color
            plaidWidth: 40, // Default value
            plaidColor1: '#000000', // Default color
            plaidColor2: '#ffffff', // Default color
            dotDiameter: 20, // Default value
            dotColor: '#000000' // Default color
          };
          
          // Create pattern with the original or filtered image
          const pattern = buildPattern(imageSrc, 512, settings);
          
          // Apply filters directly to the pattern if needed
          if (patternState.filters) {
            const { hue, saturation, brightness, contrast } = patternState.filters;
            const filterStyle = [
              `hue-rotate(${hue}deg)`,
              `saturate(${saturation}%)`,
              `brightness(${brightness}%)`,
              `contrast(${contrast}%)`
            ].join(' ');
            
            return (
              <g style={{ filter: filterStyle }}>
                <defs>
                  {pattern.defs}
                </defs>
                <rect
                  width={VIEWBOX_SIZE}
                  height={VIEWBOX_SIZE}
                  fill={pattern.fillUrl}
                  opacity={patternState.opacity}
                />
              </g>
            );
          }
          
          return (
            <g>
              <defs>
                {pattern.defs}
              </defs>
              <rect
                width={VIEWBOX_SIZE}
                height={VIEWBOX_SIZE}
                fill={pattern.fillUrl}
                opacity={patternState.opacity}
              />
            </g>
          );
        } catch (error) {
          console.warn('Pattern rendering failed, falling back to single image', error);
          // Fallback to single image if pattern rendering fails
        }
      }
      
      // Default to single centered image if no patternState or pattern rendering fails
      const size = VIEWBOX_SIZE * 0.8; // Make the image 80% of the viewport
      const x = (VIEWBOX_SIZE - size) / 2; // Center the image
      
      return (
        <g>
          <image
            href={imageSrc}
            x={x}
            y={x}
            width={size}
            height={size}
            preserveAspectRatio="xMidYMid meet"
          />
        </g>
      );
    };

    // Dynamic container background based on pattern background
    const getContainerStyle = () => {
      // Only use backdrop blur for transparent backgrounds
      // Let the SVG background handle all other background types
      if (background.type === 'transparent') {
        return "w-full h-full max-w-[85vh] max-h-[85vh] aspect-square rounded-2xl overflow-hidden bg-white/70 backdrop-blur-xl border border-white/30 shadow-2xl shadow-black/20 ring-1 ring-inset ring-white/50";
      }
      // For solid and gradient backgrounds, use transparent container so SVG background shows through
      return "w-full h-full max-w-[85vh] max-h-[85vh] aspect-square rounded-2xl overflow-hidden bg-transparent border border-white/30 shadow-2xl shadow-black/20 ring-1 ring-inset ring-white/50";
    };

    return (
      <div 
        className={getContainerStyle()}
        ref={ref}
      >
        <svg viewBox={`0 0 ${VIEWBOX_SIZE} ${VIEWBOX_SIZE}`} width="100%" height="100%" preserveAspectRatio="xMidYMid slice">
          <defs>
            {removeBackground && backgroundRemovalFilter('pattern-bg-remove')}
          </defs>
          <g style={{
            transformOrigin: 'center center',
            transform: `scale(${zoom}) rotate(${globalRotation}deg)`,
            transition: 'transform 0.2s ease-out'
          }}>
            <g>
              {renderBg()}
            </g>
            <g filter={removeBackground ? 'url(#pattern-bg-remove)' : undefined}>
              {layers.map(renderLayer)}
              {imageSrc && renderUploadedImage()}
            </g>
          </g>
        </svg>
      </div>
    );
  }
);

PatternCanvas.displayName = 'PatternCanvas';

export default PatternCanvas;