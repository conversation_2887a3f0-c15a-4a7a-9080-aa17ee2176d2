import { PatternState, Background } from './types';

// Global cache for generated tile canvases keyed by image src + filters + size + flip
const globalTileCache = new Map<string, HTMLCanvasElement>();

export const drawPattern = (
  ctx: CanvasRenderingContext2D,
  image: HTMLImageElement,
  patternState: PatternState,
  background: Background,
  globalRotation: number,
  zoom: number,
  width: number,
  height: number,
  showOverlay: boolean = true
) => {
  // 1. Clear and set background
  ctx.clearRect(0, 0, width, height);
  if (background.type !== 'transparent') {
    if (background.type === 'solid') {
      ctx.fillStyle = background.color || '#ffffff';
      ctx.fillRect(0, 0, width, height);
    } else if (background.type === 'gradient') {
      const gradient = ctx.createLinearGradient(0, 0, width, height);
      gradient.addColorStop(0, background.startColor || '#7dd3fc');
      gradient.addColorStop(1, background.endColor || '#f9a8d4');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
    }
  }

  // 2. Apply global transformations
  ctx.save();
  ctx.translate(width / 2, height / 2);
  ctx.scale(zoom, zoom);
  ctx.rotate((globalRotation * Math.PI) / 180);
  ctx.translate(-width / 2, -height / 2);

  // 3. Generate pattern positions
  const { gridCountX, gridCountY, spacing, patternType, alternate, seamless } = patternState;

  // Interpret scale as a percentage where 100 = 1.0 (no change), 200 = 2.0, 0 = 0.0
  const scaleMultiplier = Math.max(0, patternState.scale) / 100;

  // Spacing is an explicit pixel gap between elements (user-configurable).
  // Keep spacing independent of the image 'scale' so the user controls spacing directly.
  const effectiveSpacing = spacing;

  // Nominal cell based on canvas size (do NOT subtract spacing here).
  // The element size is controlled by scale, spacing is applied in addition to element size
  // so increasing "spacing" increases the gap between images (grid lines extend).
  const nominalCellWidth = width / gridCountX;
  const nominalCellHeight = height / gridCountY;

  // Element size inside each logical cell is the nominal cell scaled by the scaleMultiplier.
  // This means "size/scale" changes the image size but DOES NOT change the grid-line length.
  const elementWidth = nominalCellWidth * scaleMultiplier;
  const elementHeight = nominalCellHeight * scaleMultiplier;

  // Compute total footprint of the pattern (may be larger than canvas if scale/spacing grow)
  const totalPatternWidth = gridCountX * elementWidth + Math.max(0, gridCountX - 1) * effectiveSpacing;
  const totalPatternHeight = gridCountY * elementHeight + Math.max(0, gridCountY - 1) * effectiveSpacing;

  // Start positions so pattern is centered in the canvas (elements are positioned by their centers)
  const startX = (width - totalPatternWidth) / 2 + elementWidth / 2;
  const startY = (height - totalPatternHeight) / 2 + elementHeight / 2;

  // Build positions array for tiles (positions are allowed to be outside canvas bounds when footprint grows)
  const positions: Array<{ x: number; y: number; index: number; flip?: boolean }> = [];
  for (let row = 0; row < gridCountY; row++) {
    for (let col = 0; col < gridCountX; col++) {
      let x = startX + col * (elementWidth + effectiveSpacing);
      let y = startY + row * (elementHeight + effectiveSpacing);

      if (patternType === 'diamond' && row % 2 === 1) {
        x += (elementWidth + effectiveSpacing) / 2;
      }
      if (patternType === 'checkerboard' && (row + col) % 2 === 1) {
        continue;
      }

      const flip = alternate && ((row + col) % 2 === 1);
      positions.push({ x, y, index: row * gridCountX + col, flip });
    }
  }

  // 4. Draw pattern
  const { scale, rotation, opacity, filters } = patternState;
  const baseImageWidth = image.width;
  const baseImageHeight = image.height;
  const imageAspectRatio = baseImageWidth / baseImageHeight;

  // Use a reference cell size that ignores spacing so image scale is independent of spacing
  const refCellWidth = width / gridCountX;
  const refCellHeight = height / gridCountY;
  const baseSize = Math.min(refCellWidth, refCellHeight);

  // Helper: create or get a cached tile canvas for given parameters (used by image fills)
  const getTileCanvas = (flipFlag: boolean, drawW: number, drawH: number) => {
    const key = `${image.src || 'img'}|h${filters.hue}|s${filters.saturation}|b${filters.brightness}|c${filters.contrast}|f${flipFlag ? 1 : 0}|w${Math.round(drawW)}|h${Math.round(drawH)}`;
    let cached = globalTileCache.get(key);
    if (cached) return cached;

    const tileCanvas = document.createElement('canvas');
    tileCanvas.width = Math.max(1, Math.round(drawW));
    tileCanvas.height = Math.max(1, Math.round(drawH));
    const tctx = tileCanvas.getContext('2d');
    if (tctx) {
      tctx.clearRect(0, 0, tileCanvas.width, tileCanvas.height);
      tctx.filter = `hue-rotate(${filters.hue}deg) saturate(${filters.saturation}%) brightness(${filters.brightness}%) contrast(${filters.contrast}%)`;
      tctx.save();
      if (flipFlag) {
        tctx.translate(tileCanvas.width, 0);
        tctx.scale(-1, 1);
      }
      tctx.drawImage(image, 0, 0, tileCanvas.width, tileCanvas.height);
      tctx.restore();
    }
    globalTileCache.set(key, tileCanvas);
    return tileCanvas;
  };

  // Utility: draw a filled rotated diamond centered at (cx,cy)
  const drawDiamond = (cx: number, cy: number, w: number, h: number, fillStyle: string | null, outline?: { color: string; width: number }, imageFill?: boolean) => {
    ctx.save();
    ctx.translate(cx, cy);
    ctx.rotate((rotation * Math.PI) / 180);
    ctx.beginPath();
    ctx.moveTo(0, -h / 2);
    ctx.lineTo(w / 2, 0);
    ctx.lineTo(0, h / 2);
    ctx.lineTo(-w / 2, 0);
    ctx.closePath();
    if (imageFill && image) {
      // Clip to diamond and draw image stretched to diamond bbox, applying current filters
      ctx.save();
      ctx.clip();
      // Apply filters (same as used for cached tiles)
      const filterStr = `hue-rotate(${filters.hue}deg) saturate(${filters.saturation}%) brightness(${filters.brightness}%) contrast(${filters.contrast}%)`;
      const prevFilter = (ctx as any).filter;
      try {
        (ctx as any).filter = filterStr;
        ctx.drawImage(image, -w / 2, -h / 2, w, h);
      } finally {
        (ctx as any).filter = prevFilter || 'none';
      }
      ctx.restore();
    } else if (fillStyle) {
      ctx.fillStyle = fillStyle;
      ctx.fill();
    }
    if (outline && outline.width > 0) {
      ctx.lineWidth = outline.width;
      ctx.strokeStyle = outline.color;
      ctx.stroke();
    }
    ctx.restore();
  };

  // If argyle, render a diamond-based argyle pattern
  if (patternType === 'argyle') {
    const cfg = patternState.argyle || {};
    const diamondScale = (cfg.diamondScale ?? 100) / 100;
    const fillMode = cfg.fillMode || 'color';
    const primaryColor = cfg.primaryColor || '#ffffff';
    const secondaryColor = cfg.secondaryColor || '#c0392b';
    const outlineEnabled = !!cfg.outlineEnabled;
    const outlineColor = cfg.outlineColor || '#000000';
    const outlineThickness = cfg.outlineThickness ?? 1;
    const staggered = cfg.staggered ?? true;
    const ribEnabled = cfg.ribEnabled ?? true;
    const ribColor = cfg.ribColor || '#2c3e50';
    const ribThickness = cfg.ribThickness ?? 1;
    const ribSpacing = cfg.ribSpacing ?? Math.max(20, spacing * 4);
    const ribAngle = cfg.ribAngle || 'both';
    const ribMargin = cfg.ribMargin ?? 4;
    const ribOverlay = cfg.ribOverlay || 'clipped';
    // If ribs are preview-only and this draw is for export (showOverlay === false), disable ribs
    const ribEnabledEffective = ribEnabled && !(cfg.ribPreviewOnly === true && !showOverlay);

    // diamond size derived from elementWidth/elementHeight and diamondScale
    const diamondW = elementWidth * diamondScale;
    const diamondH = elementHeight * diamondScale;

    // Footprint corners
    const footprintLeft = startX - elementWidth / 2;
    const footprintTop = startY - elementHeight / 2;
    const footprintW = totalPatternWidth;
    const footprintH = totalPatternHeight;
    const footprintRight = footprintLeft + footprintW;
    const footprintBottom = footprintTop + footprintH;

    // Helper to draw ribs (diagonal lines across footprint)
    const drawRibs = () => {
      ctx.save();
      ctx.strokeStyle = ribColor;
      ctx.lineWidth = Math.max(0.5, ribThickness * (Math.max(1, scale / 100)));
      ctx.lineCap = 'butt';

      // draw lines at +45 and/or -45 across bounding box
      const drawDiag = (angleSign: 1 | -1) => {
        // parameterize lines by intercept along axis; step by ribSpacing
        // For +45 lines (y = -x + c), vary c across range
        // We'll sample c from -maxDim to maxDim
        const maxDim = Math.hypot(footprintW, footprintH) * 1.5;
        for (let c = -maxDim; c <= maxDim; c += ribSpacing) {
          ctx.beginPath();
          // compute line endpoints clipped to footprint rect
          // Solve intersection of line y = angleSign * x + b with rectangle
          const b = c;
          const pts: Array<[number, number]> = [];
          // test intersection with 4 edges
          // left x = footprintLeft
          let y = angleSign * footprintLeft + b;
          if (y >= footprintTop - 1 && y <= footprintBottom + 1) pts.push([footprintLeft, y]);
          // right x = footprintRight
          y = angleSign * footprintRight + b;
          if (y >= footprintTop - 1 && y <= footprintBottom + 1) pts.push([footprintRight, y]);
          // top y = footprintTop => x = (y - b)/angleSign
          let x = (footprintTop - b) / angleSign;
          if (x >= footprintLeft - 1 && x <= footprintRight + 1) pts.push([x, footprintTop]);
          // bottom y = footprintBottom
          x = (footprintBottom - b) / angleSign;
          if (x >= footprintLeft - 1 && x <= footprintRight + 1) pts.push([x, footprintBottom]);

          if (pts.length >= 2) {
            // sort points to draw between them
            const [p0, p1] = pts.slice(0,2);
            ctx.moveTo(p0[0], p0[1]);
            ctx.lineTo(p1[0], p1[1]);
            ctx.stroke();
          }
        }
      };

      if (ribAngle === 'diag1' || ribAngle === 'both') drawDiag(1);
      if (ribAngle === 'diag2' || ribAngle === 'both') drawDiag(-1);

      ctx.restore();
    };

    // If ribs are clipped, draw ribs first then diamonds on top to hide ribs under diamonds.
    // If overlay, draw diamonds first then ribs on top.
    if (ribEnabled && ribOverlay === 'clipped') {
      drawRibs();
    }

    // Draw base and staggered diamonds
    for (let row = 0; row < gridCountY; row++) {
      for (let col = 0; col < gridCountX; col++) {
        const cx = startX + col * (elementWidth + effectiveSpacing);
        const cy = startY + row * (elementHeight + effectiveSpacing);

        // primary layer: color choice alternates to produce argyle look
        const useSecondary = staggered && ((row + col) % 2 === 1);
        const fillColor = useSecondary ? secondaryColor : primaryColor;

        if (fillMode === 'image' && image) {
          drawDiamond(cx, cy, diamondW, diamondH, null, outlineEnabled ? { color: outlineColor, width: outlineThickness } : undefined, true);
        } else {
          drawDiamond(cx, cy, diamondW, diamondH, fillColor, outlineEnabled ? { color: outlineColor, width: outlineThickness } : undefined, false);
        }
      }
    }

    // If we want a second offset layer (staggered) with different color, draw offset centers
    if (staggered) {
      for (let row = 0; row < gridCountY; row++) {
        for (let col = 0; col < gridCountX; col++) {
          const cx = startX + col * (elementWidth + effectiveSpacing) + (elementWidth + effectiveSpacing) / 2;
          const cy = startY + row * (elementHeight + effectiveSpacing) + (elementHeight + effectiveSpacing) / 2;
          // only draw if within footprint
          if (cx < footprintLeft || cx > footprintRight || cy < footprintTop || cy > footprintBottom) continue;
          if (fillMode === 'image' && image) {
            drawDiamond(cx, cy, diamondW, diamondH, null, outlineEnabled ? { color: outlineColor, width: outlineThickness } : undefined, true);
          } else {
            drawDiamond(cx, cy, diamondW, diamondH, secondaryColor, outlineEnabled ? { color: outlineColor, width: outlineThickness } : undefined, false);
          }
        }
      }
    }

    // If ribs overlay is requested, draw ribs now on top
    if (ribEnabled && ribOverlay === 'overlay') {
      drawRibs();
    }

    // Argyle handling done - proceed to grid-lines / overlay below
  } else {
    // Default tile drawing (original behavior)
    positions.forEach(({ x, y, flip }) => {
      const altScaleMultiplier = flip ? 0.9 : 1;
      const drawW = Math.max(1, elementWidth * altScaleMultiplier);
      const drawH = Math.max(1, drawW / imageAspectRatio);

      const angleRad = (rotation * Math.PI) / 180;
      const cosA = Math.abs(Math.cos(angleRad));
      const sinA = Math.abs(Math.sin(angleRad));
      const bboxW = drawW * cosA + drawH * sinA;
      const bboxH = drawW * sinA + drawH * cosA;

      if (seamless) {
        const left = x - bboxW / 2;
        const right = x + bboxW / 2;
        const top = y - bboxH / 2;
        const bottom = y + bboxH / 2;

        const periodX = Math.abs(totalPatternWidth) > 0 ? totalPatternWidth : width;
        const periodY = Math.abs(totalPatternHeight) > 0 ? totalPatternHeight : height;

        const minI = Math.floor((-right) / periodX);
        const maxI = Math.ceil((width - left) / periodX);
        const minJ = Math.floor((-bottom) / periodY);
        const maxJ = Math.ceil((height - top) / periodY);

        for (let i = minI; i <= maxI; i++) {
          for (let j = minJ; j <= maxJ; j++) {
            const tx = x + i * periodX;
            const ty = y + j * periodY;
            const tileCanvas = getTileCanvas(!!flip, drawW, drawH);
            // draw tile rotated/positioned
            ctx.save();
            ctx.translate(tx, ty);
            ctx.rotate((rotation * Math.PI) / 180);
            ctx.globalAlpha = opacity;
            ctx.drawImage(tileCanvas, -tileCanvas.width / 2, -tileCanvas.height / 2);
            ctx.restore();
          }
        }
      } else {
        const tileCanvas = getTileCanvas(!!flip, drawW, drawH);
        ctx.save();
        ctx.translate(x, y);
        ctx.rotate((rotation * Math.PI) / 180);
        ctx.globalAlpha = opacity;
        ctx.drawImage(tileCanvas, -tileCanvas.width / 2, -tileCanvas.height / 2);
        ctx.restore();
      }
    });
  }

  // --------------- Grid-line rendering (feature) ---------------
  // Draw visible grid lines between tiles while skipping over the tile areas so
  // lines appear to start/end a margin distance away from each image.
  const gridCfg = patternState.gridLines || { enabled: false };
  if (gridCfg.enabled) {
    ctx.save();
    // Compute footprint and boundaries (same as overlay math)
    const footprintLeft = startX - elementWidth / 2;
    const footprintTop = startY - elementHeight / 2;
    const footprintW = totalPatternWidth;
    const footprintH = totalPatternHeight;

    // Determine stroke color: either explicit or sampled from the image
    const resolveLineColor = () => {
      if (gridCfg.linkColorToImage && image) {
        // Sample a small area from center of the image and average RGB
        try {
          const sampleCanvas = document.createElement('canvas');
          sampleCanvas.width = 4;
          sampleCanvas.height = 4;
          const sctx = sampleCanvas.getContext('2d');
          if (sctx) {
            sctx.drawImage(image, 0, 0, sampleCanvas.width, sampleCanvas.height);
            const data = sctx.getImageData(0, 0, sampleCanvas.width, sampleCanvas.height).data;
            let r = 0, g = 0, b = 0, count = 0;
            for (let i = 0; i < data.length; i += 4) {
              r += data[i]; g += data[i+1]; b += data[i+2]; count++;
            }
            r = Math.round(r / count); g = Math.round(g / count); b = Math.round(b / count);
            const toHex = (v: number) => v.toString(16).padStart(2, '0');
            return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
          }
        } catch (e) {
          // fallback to configured color
        }
      }
      return gridCfg.color || '#3b82f6';
    };

    const lineColor = resolveLineColor();
    // Thickness scales with user thickness and current pattern scale so it "grows" with scale
    const lineWidth = Math.max(0.5, (gridCfg.thickness || 1) * (Math.max(1, Math.max(1, scale / 100))));
    ctx.strokeStyle = lineColor;
    ctx.lineWidth = lineWidth;
    ctx.lineCap = 'butt';

    // Scaled margin to keep lines away from images (scale margin with scale)
    const scaledMargin = (gridCfg.margin || 0) * (Math.max(1, Math.max(1, scale / 100)));

    // Vertical boundaries
    for (let c = 0; c <= gridCountX; c++) {
      const gx = footprintLeft + c * (elementWidth + effectiveSpacing);
      // Build gap intervals from tiles that touch/neighbor this boundary.
      const gaps: Array<{ start: number; end: number }> = [];
      for (let row = 0; row < gridCountY; row++) {
        // tile center for this row & neighbor column to compute vertical overlap
        const centerY = startY + row * (elementHeight + effectiveSpacing);
        const tileHalfH = (elementHeight) / 2;
        const gapStart = centerY - (tileHalfH + scaledMargin);
        const gapEnd = centerY + (tileHalfH + scaledMargin);
        gaps.push({ start: gapStart, end: gapEnd });
      }
      // Merge gaps that might overlap (defensive)
      gaps.sort((a,b) => a.start - b.start);
      const merged: Array<{ start: number; end: number }> = [];
      for (const g of gaps) {
        if (!merged.length) merged.push({ ...g });
        else {
          const last = merged[merged.length-1];
          if (g.start <= last.end) last.end = Math.max(last.end, g.end);
          else merged.push({ ...g });
        }
      }
      // Draw line segments between merged gaps within footprintTop..footprintTop+footprintH
      let y0 = footprintTop;
      for (const m of merged) {
        const segStart = Math.max(footprintTop, y0);
        const segEnd = Math.min(footprintTop + footprintH, m.start);
        if (segEnd > segStart + 0.5) {
          ctx.beginPath();
          ctx.moveTo(gx, segStart);
          ctx.lineTo(gx, segEnd);
          ctx.stroke();
        }
        y0 = Math.max(y0, m.end);
      }
      // final segment after last gap
      if (y0 < footprintTop + footprintH - 0.5) {
        ctx.beginPath();
        ctx.moveTo(gx, y0);
        ctx.lineTo(gx, footprintTop + footprintH);
        ctx.stroke();
      }
    }

    // Horizontal boundaries
    for (let r = 0; r <= gridCountY; r++) {
      const gy = footprintTop + r * (elementHeight + effectiveSpacing);
      const gaps: Array<{ start: number; end: number }> = [];
      for (let col = 0; col < gridCountX; col++) {
        const centerX = startX + col * (elementWidth + effectiveSpacing);
        const tileHalfW = (elementWidth) / 2;
        const gapStart = centerX - (tileHalfW + scaledMargin);
        const gapEnd = centerX + (tileHalfW + scaledMargin);
        gaps.push({ start: gapStart, end: gapEnd });
      }
      gaps.sort((a,b) => a.start - b.start);
      const merged: Array<{ start: number; end: number }> = [];
      for (const g of gaps) {
        if (!merged.length) merged.push({ ...g });
        else {
          const last = merged[merged.length-1];
          if (g.start <= last.end) last.end = Math.max(last.end, g.end);
          else merged.push({ ...g });
        }
      }
      let x0 = footprintLeft;
      for (const m of merged) {
        const segStart = Math.max(footprintLeft, x0);
        const segEnd = Math.min(footprintLeft + footprintW, m.start);
        if (segEnd > segStart + 0.5) {
          ctx.beginPath();
          ctx.moveTo(segStart, gy);
          ctx.lineTo(segEnd, gy);
          ctx.stroke();
        }
        x0 = Math.max(x0, m.end);
      }
      if (x0 < footprintLeft + footprintW - 0.5) {
        ctx.beginPath();
        ctx.moveTo(x0, gy);
        ctx.lineTo(footprintLeft + footprintW, gy);
        ctx.stroke();
      }
    }

    ctx.restore();
  }

  // Debug overlay: draw computed footprint and grid lines to help visualize spacing/size/scale
  if (showOverlay) {
    ctx.save();
    ctx.globalAlpha = 0.18;

    // Footprint rectangle (unrotated, in the same transformed coordinate space as tiles)
    const footprintLeft = startX - elementWidth / 2;
    const footprintTop = startY - elementHeight / 2;
    const footprintW = totalPatternWidth;
    const footprintH = totalPatternHeight;

    // translucent fill + stroked border
    ctx.fillStyle = 'rgba(14,165,233,0.06)'; // light blue fill
    ctx.fillRect(footprintLeft, footprintTop, footprintW, footprintH);
    ctx.lineWidth = 1;
    ctx.strokeStyle = 'rgba(59,130,246,0.35)';
    ctx.strokeRect(footprintLeft, footprintTop, footprintW, footprintH);

    // Grid lines (draw cell boundaries so spacing is visible)
    ctx.strokeStyle = 'rgba(59,130,246,0.22)';
    ctx.lineWidth = 1;
    // vertical boundaries (0..gridCountX)
    for (let c = 0; c <= gridCountX; c++) {
      const gx = footprintLeft + c * (elementWidth + effectiveSpacing);
      ctx.beginPath();
      ctx.moveTo(gx, footprintTop);
      ctx.lineTo(gx, footprintTop + footprintH);
      ctx.stroke();
    }
    // horizontal boundaries (0..gridCountY)
    for (let r = 0; r <= gridCountY; r++) {
      const gy = footprintTop + r * (elementHeight + effectiveSpacing);
      ctx.beginPath();
      ctx.moveTo(footprintLeft, gy);
      ctx.lineTo(footprintLeft + footprintW, gy);
      ctx.stroke();
    }

    // center crosshair
    ctx.strokeStyle = 'rgba(16,185,129,0.5)';
    ctx.beginPath();
    ctx.moveTo(startX, footprintTop);
    ctx.lineTo(startX, footprintTop + footprintH);
    ctx.moveTo(footprintLeft, startY);
    ctx.lineTo(footprintLeft + footprintW, startY);
    ctx.stroke();

    ctx.restore();
  }

  // 5. Restore global state
  ctx.restore();
};
