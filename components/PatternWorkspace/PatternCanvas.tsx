import React, { useRef, useEffect } from 'react';
import { drawPattern } from './drawingUtils';
import { PatternState, Background } from './types';



interface PatternCanvasProps {
  imageSrc: string | null;
  patternState: PatternState;
  background: Background;
  globalRotation: number;
  zoom: number;
  showOverlay?: boolean;
}

export default function PatternCanvas({ 
  imageSrc, 
  patternState, 
  background, 
  globalRotation, 
  zoom,
  showOverlay = true
}: PatternCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = 'anonymous';

    const onLoadAndDraw = () => {
      drawPattern(ctx, img, patternState, background, globalRotation, zoom, canvas.width, canvas.height, showOverlay);
    };

    if (imageSrc) {
      img.onload = onLoadAndDraw;
      img.src = imageSrc;
    } else {
      // No image uploaded — create a 1x1 transparent placeholder so drawPattern can run
      const placeholder = document.createElement('canvas');
      placeholder.width = 1;
      placeholder.height = 1;
      const pctx = placeholder.getContext('2d');
      if (pctx) {
        pctx.clearRect(0, 0, 1, 1);
      }
      img.onload = onLoadAndDraw;
      img.src = placeholder.toDataURL();
    }

  }, [imageSrc, patternState, background, globalRotation, zoom, showOverlay]);
  const itemCount = patternState.gridCountX * patternState.gridCountY;

  return (
    <div className="bg-white/70 backdrop-blur-xl border border-white/30 rounded-2xl p-6 shadow-2xl">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-gray-700">Pattern Preview</h3>
        <div className="text-sm text-gray-500">
          {itemCount} items • {patternState.patternType} layout
        </div>
      </div>
      
      <div className="relative">
        <canvas
          ref={canvasRef}
          width={800}
          height={600}
          className="w-full h-auto border border-gray-300 rounded-lg"
        />
        
        {!imageSrc && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
            <div className="text-center text-gray-500">
              <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-lg font-medium">Upload an image to start</p>
              <p className="text-sm">Your pattern will appear here</p>
            </div>
          </div>
        )}
      </div>

      {imageSrc && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-blue-800">Pattern:</span>
              <span className="ml-1 text-blue-700 capitalize">{patternState.patternType}</span>
            </div>
            <div>
              <span className="font-medium text-blue-800">Items:</span>
              <span className="ml-1 text-blue-700">{itemCount}</span>
            </div>
            <div>
              <span className="font-medium text-blue-800">Grid:</span>
              <span className="ml-1 text-blue-700">{patternState.gridCountX} × {patternState.gridCountY}</span>
            </div>
            <div>
              <span className="font-medium text-blue-800">Scale:</span>
              <span className="ml-1 text-blue-700">{patternState.scale}%</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
