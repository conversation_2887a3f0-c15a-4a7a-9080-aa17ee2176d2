import React from 'react';
import { GridIcon, DiamondIcon, CheckerboardIcon } from '../icons';
import { PatternState } from './types';

interface PatternControlsProps {
  state: PatternState;
  setState: React.Dispatch<React.SetStateAction<PatternState>>;
  disabled?: boolean;
}

const Slider: React.FC<{
  label: string; 
  value: number; 
  min: number; 
  max: number; 
  step?: number;
  onChange: (value: number) => void; 
  unit?: string; 
  disabled?: boolean;
  displayValue?: string;
}> = ({ label, value, min, max, step = 1, onChange, unit = '', disabled = false, displayValue }) => {
  // Use a ref for dragging state so document-level listeners see updates immediately
  const [isDragging, setIsDragging] = React.useState(false);
  const isDraggingRef = React.useRef(false);
  const sliderRef = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Helper to snap value to step
  const snapToStep = (raw: number) => {
    const range = max - min;
    if (step <= 0) return Math.min(max, Math.max(min, raw));
    const stepped = Math.round((raw - min) / step) * step + min;
    return Math.min(max, Math.max(min, stepped));
  };

  const updateFromClientX = (clientX: number) => {
    if (!sliderRef.current) return;
    const rect = sliderRef.current.getBoundingClientRect();
    const x = Math.min(Math.max(clientX - rect.left, 0), rect.width);
    const percent = rect.width === 0 ? 0 : x / rect.width;
    const rawValue = min + (max - min) * percent;
    const newValue = snapToStep(rawValue);
    if (inputRef.current) {
      // keep native input in sync for immediate feedback
      inputRef.current.value = String(newValue);
    }
    onChange(newValue);
  };

  const handlePointerDown = (e: React.PointerEvent) => {
    if (disabled) return;
    // capture pointer so we continue to receive events even if the cursor leaves the element
    (e.target as Element).setPointerCapture?.(e.pointerId);
    isDraggingRef.current = true;
    setIsDragging(true);
    // attach document-level mouse handlers for backwards compatibility
    const pmove = (ev: PointerEvent) => {
      if (!isDraggingRef.current) return;
      updateFromClientX((ev as PointerEvent).clientX);
    };
    const mup = (ev: PointerEvent) => {
      isDraggingRef.current = false;
      setIsDragging(false);
      document.removeEventListener('pointermove', pmove);
      document.removeEventListener('pointerup', mup);
    };
    document.addEventListener('pointermove', pmove);
    document.addEventListener('pointerup', mup);
    // initial update
    updateFromClientX(e.clientX);
  };

  // fallback mouse handlers (for older browsers)
  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled) return;
    isDraggingRef.current = true;
    setIsDragging(true);
    const mm = (ev: MouseEvent) => {
      if (!isDraggingRef.current) return;
      updateFromClientX(ev.clientX);
    };
    const mu = () => {
      isDraggingRef.current = false;
      setIsDragging(false);
      document.removeEventListener('mousemove', mm);
      document.removeEventListener('mouseup', mu);
    };
    document.addEventListener('mousemove', mm);
    document.addEventListener('mouseup', mu);
    updateFromClientX(e.clientX);
  };

  const handleTrackClick = (e: React.MouseEvent) => {
    if (disabled) return;
    updateFromClientX(e.clientX);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const n = snapToStep(Number(e.target.value));
    onChange(n);
  };

  return (
    <div className="space-y-1.5">
      <div className="flex justify-between items-center text-sm font-medium text-gray-700">
        <label className={disabled ? 'text-gray-400' : ''}>{label}</label>
        <span className={disabled ? 'text-gray-400' : 'text-gray-800'}>
          {displayValue || `${value}${unit}`}
        </span>
      </div>
      <div 
        ref={sliderRef}
        className="relative h-2 bg-black/10 rounded-lg cursor-pointer"
        onPointerDown={handlePointerDown}
        onMouseDown={handleMouseDown}
        onClick={handleTrackClick}
      >
        <div 
          className="absolute h-full bg-sky-500/30 rounded-l-lg"
          style={{ width: `${((value - min) / (max - min)) * 100}%` }}
        />
        <input 
          ref={inputRef}
          type="range" 
          min={min} 
          max={max} 
          step={step} 
          value={value} 
          onChange={(e) => onChange(Number(e.target.value))} 
          disabled={disabled}
          className="absolute w-full h-full opacity-0 cursor-pointer"
        />
        <div 
          className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-sky-500 rounded-full shadow"
          onPointerDown={handlePointerDown}
          onMouseDown={handleMouseDown}
          style={{ 
            left: `${((value - min) / (max - min)) * 100}%`,
            transform: 'translate(-50%, -50%)',
            cursor: 'pointer'
          }}
        />
      </div>
    </div>
  );
};

const ColorInput: React.FC<{ 
  label: string; 
  value: string; 
  onChange: (value: string) => void; 
}> = ({ label, value, onChange }) => (
  <div className="flex items-center gap-2 bg-white/50 border border-gray-900/10 rounded-md p-1.5">
    <input 
      type="color" 
      value={value} 
      onChange={(e) => onChange(e.target.value)} 
      className="w-8 h-8 p-0 border-none bg-transparent appearance-none cursor-pointer rounded-md" 
    />
    <input 
      type="text" 
      value={value} 
      onChange={(e) => onChange(e.target.value)} 
      className="bg-transparent w-full focus:outline-none text-gray-800 placeholder:text-gray-500" 
      placeholder={label} 
    />
  </div>
);

export default function PatternControls({ state, setState, disabled = false }: PatternControlsProps) {
  const updateState = (updates: Partial<PatternState>) => {
    // Use functional updates to avoid race conditions / stale state when multiple sliders change
    setState(prev => ({ ...prev, ...updates }));
  };

  const patternTypes = [
    { type: 'grid' as const, icon: <GridIcon/>, label: 'Grid' },
    { type: 'checkerboard' as const, icon: <CheckerboardIcon/>, label: 'Checkerboard' },
    { type: 'diamond' as const, icon: <DiamondIcon/>, label: 'Diamond' },
    { type: 'argyle' as const, icon: <DiamondIcon/>, label: 'Argyle' }
  ];

  return (
    <div className={`bg-white/70 backdrop-blur-xl border border-white/30 rounded-2xl p-6 shadow-2xl transition-opacity ${disabled ? 'opacity-50' : 'opacity-100'}`}>
      <h3 className="font-semibold text-gray-700 mb-4">Pattern Settings</h3>
      
      <div className="space-y-6">
        
        {/* Pattern Type Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-gray-700">Pattern Type</label>
          <div className="grid grid-cols-3 gap-2">
            {patternTypes.map(({ type, icon, label }) => (
              <button 
                key={type} 
                onClick={() => updateState({ patternType: type })}
                className={`flex flex-col items-center justify-center p-3 rounded-lg transition-colors ${
                  state.patternType === type 
                    ? 'bg-sky-500 text-white' 
                    : 'bg-white/50 hover:bg-white/70 text-gray-700'
                }`}
              >
                <span className="w-5 h-5 mb-1">{icon}</span>
                <span className="text-xs font-medium">{label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Scale and Spacing */}
        <div className="space-y-4">
          <Slider 
            label="Image Scale" 
            value={state.scale} 
            onChange={(value) => updateState({ scale: value })} 
            min={0} 
            max={200} 
            step={1}
            unit="%" 
            displayValue={`${state.scale}%`}
          />
          <Slider 
            label="Spacing" 
            value={state.spacing} 
            onChange={(value) => updateState({ spacing: value })} 
            min={0} 
            max={200} 
            step={2}
            unit="px" 
          />
          <Slider 
            label="Image Rotation" 
            value={state.rotation} 
            onChange={(value) => updateState({ rotation: value })} 
            min={0} 
            max={360} 
            unit="°" 
          />
        </div>

        {/* Image Effects */}
        <div className="space-y-4 pt-2 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700">Image Effects</h4>
          
          {/* Opacity */}
          <Slider 
            label="Transparency" 
            value={Math.round(state.opacity * 100)} 
            onChange={(value) => updateState({ opacity: value / 100 })} 
            min={0} 
            max={100} 
            unit="%" 
          />



          {/* Image Filters for Multi-color Images */}
          <div className="space-y-3 pt-2 border-t border-gray-200">
            <h5 className="text-xs font-medium text-gray-700">Image Adjustments</h5>
            <Slider 
              label="Hue Shift" 
              value={state.filters.hue} 
              onChange={(value) => setState(prev => ({ 
                ...prev, 
                filters: { ...prev.filters, hue: value } 
              }))} 
              min={0} 
              max={360} 
              unit="°" 
            />
            <Slider 
              label="Saturation" 
              value={state.filters.saturation} 
              onChange={(value) => setState(prev => ({ 
                ...prev, 
                filters: { ...prev.filters, saturation: value } 
              }))} 
              min={0} 
              max={200} 
              unit="%" 
            />
            <Slider 
              label="Brightness" 
              value={state.filters.brightness} 
              onChange={(value) => setState(prev => ({ 
                ...prev, 
                filters: { ...prev.filters, brightness: value } 
              }))} 
              min={0} 
              max={200} 
              unit="%" 
            />
            <Slider 
              label="Contrast" 
              value={state.filters.contrast} 
              onChange={(value) => setState(prev => ({ 
                ...prev, 
                filters: { ...prev.filters, contrast: value } 
              }))} 
              min={0} 
              max={200} 
              unit="%" 
            />
          </div>
        </div>

        {/* Pattern Options */}
        <div className="space-y-3 pt-2 border-t border-gray-200">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="alternatePattern"
              checked={state.alternate}
              onChange={(e) => updateState({ alternate: e.target.checked })}
              className="w-4 h-4 text-sky-500 rounded focus:ring-sky-500"
            />
            <label htmlFor="alternatePattern" className="text-sm font-medium text-gray-700">
              Alternate Pattern
            </label>
          </div>
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="seamless"
              checked={state.seamless}
              onChange={(e) => updateState({ seamless: e.target.checked })}
              className="w-4 h-4 text-sky-500 rounded focus:ring-sky-500"
            />
            <label htmlFor="seamless" className="text-sm font-medium text-gray-700">
              Seamless
            </label>
          </div>

          {/* Grid Lines Feature */}
          <div className="pt-4 border-t border-gray-100 space-y-3">
            <div className="flex items-center gap-3">
              <input
                id="gridLinesEnabled"
                type="checkbox"
                checked={state.gridLines?.enabled || false}
                onChange={(e) => setState(prev => ({ 
                  ...prev, 
                  gridLines: { ...(prev.gridLines || { enabled: false, color: '#3b82f6', thickness: 1, margin: 4, linkColorToImage: false }), enabled: e.target.checked } 
                }))}
                className="w-4 h-4 text-sky-500 rounded focus:ring-sky-500"
              />
              <label htmlFor="gridLinesEnabled" className="text-sm font-medium text-gray-700">
                Show grid lines
              </label>
            </div>

            <div className="grid grid-cols-2 gap-3 items-center">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Line Color</label>
                <ColorInput
                  label="Line Color"
                  value={state.gridLines?.color || '#3b82f6'}
                  onChange={(val) => setState(prev => ({ 
                    ...prev, 
                    gridLines: { ...(prev.gridLines || { enabled:false, color:'#3b82f6', thickness:1, margin:4, linkColorToImage:false }), color: val } 
                  }))}
                />
              </div>
              <div className="flex items-center gap-2">
                <input
                  id="linkLineColor"
                  type="checkbox"
                  checked={state.gridLines?.linkColorToImage || false}
                  onChange={(e) => setState(prev => ({ 
                    ...prev, 
                    gridLines: { ...(prev.gridLines || { enabled:false, color:'#3b82f6', thickness:1, margin:4, linkColorToImage:false }), linkColorToImage: e.target.checked } 
                  }))}
                  className="w-4 h-4 text-sky-500 rounded focus:ring-sky-500"
                />
                <label htmlFor="linkLineColor" className="text-xs text-gray-600">Use image color</label>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <Slider
                  label="Line Thickness"
                  value={state.gridLines?.thickness ?? 1}
                  min={0}
                  max={12}
                  step={0.5}
                  onChange={(v) => setState(prev => ({ 
                    ...prev, 
                    gridLines: { ...(prev.gridLines || { enabled:false, color:'#3b82f6', thickness:1, margin:4, linkColorToImage:false }), thickness: v } 
                  }))}
                  unit="px"
                />
              </div>
              <div>
                <Slider
                  label="Line Margin"
                  value={state.gridLines?.margin ?? 4}
                  min={0}
                  max={100}
                  step={1}
                  onChange={(v) => setState(prev => ({ 
                    ...prev, 
                    gridLines: { ...(prev.gridLines || { enabled:false, color:'#3b82f6', thickness:1, margin:4, linkColorToImage:false }), margin: v } 
                  }))}
                  unit="px"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Argyle Controls */}
        {state.patternType === 'argyle' && (
          <div className="pt-4 border-t border-gray-100 space-y-4">
            <h4 className="text-sm font-medium text-gray-700">Argyle Settings</h4>

            <div className="flex items-center gap-3">
              <label className="text-xs text-gray-600">Preset</label>
              <select
                value={state.argyle?.preset || 'custom'}
                onChange={(e) => {
                  const p = e.target.value;
                  const presetMap: Record<string, Partial<PatternState['argyle']>> = {
                    custom: {},
                    classic: {
                      diamondScale: 80,
                      fillMode: 'color',
                      primaryColor: '#ffffff',
                      secondaryColor: '#c0392b',
                      ribColor: '#2c3e50',
                      ribThickness: 1,
                      ribSpacing: 60,
                      ribAngle: 'both',
                      ribOverlay: 'clipped'
                    },
                    muted: {
                      diamondScale: 85,
                      fillMode: 'color',
                      primaryColor: '#d9d5d0',
                      secondaryColor: '#9aa39a',
                      ribColor: '#6b6b6b',
                      ribThickness: 0.8,
                      ribSpacing: 80,
                      ribAngle: 'both',
                      ribOverlay: 'clipped'
                    },
                    neon: {
                      diamondScale: 70,
                      fillMode: 'color',
                      primaryColor: '#ff00a8',
                      secondaryColor: '#7cfc00',
                      ribColor: '#00e5ff',
                      ribThickness: 1.5,
                      ribSpacing: 40,
                      ribAngle: 'both',
                      ribOverlay: 'overlay'
                    },
                    monochrome: {
                      diamondScale: 90,
                      fillMode: 'color',
                      primaryColor: '#ffffff',
                      secondaryColor: '#333333',
                      ribColor: '#111111',
                      ribThickness: 1,
                      ribSpacing: 70,
                      ribAngle: 'both',
                      ribOverlay: 'clipped'
                    }
                  };
                  const presetVals = presetMap[p] || {};
                  setState(prev => ({ 
                    ...prev, 
                    argyle: { ...(prev.argyle || {}), ...presetVals, preset: p } 
                  }));
                }}
                className="p-2 border border-gray-300 rounded-lg"
              >
                <option value="custom">Custom</option>
                <option value="classic">Classic</option>
                <option value="muted">Muted</option>
                <option value="neon">Neon</option>
                <option value="monochrome">Monochrome</option>
              </select>
            </div>

            <Slider
              label="Diamond Scale"
              value={state.argyle?.diamondScale ?? 80}
              min={20}
              max={200}
              step={1}
              onChange={(v) => setState(prev => ({ 
                ...prev, 
                argyle: { ...(prev.argyle || {}), diamondScale: v } 
              }))}
              unit="%"
              displayValue={`${state.argyle?.diamondScale ?? 80}%`}
            />

            <div className="grid grid-cols-2 gap-3 items-center">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Fill Mode</label>
                <select
                  value={state.argyle?.fillMode || 'color'}
                  onChange={(e) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), fillMode: e.target.value as 'color'|'image' } }))}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                >
                  <option value="color">Color</option>
                  <option value="image">Image</option>
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Staggered Layer</label>
                <input
                  id="argyleStagger"
                  type="checkbox"
                  checked={state.argyle?.staggered ?? true}
                  onChange={(e) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), staggered: e.target.checked } }))}
                  className="w-4 h-4 text-sky-500 rounded focus:ring-sky-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Primary Color</label>
                <ColorInput
                  label="Primary"
                  value={state.argyle?.primaryColor || '#ffffff'}
                  onChange={(val) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), primaryColor: val } }))}
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Secondary Color</label>
                <ColorInput
                  label="Secondary"
                  value={state.argyle?.secondaryColor || '#c0392b'}
                  onChange={(val) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), secondaryColor: val } }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Rib Color</label>
                <ColorInput
                  label="Rib"
                  value={state.argyle?.ribColor || '#2c3e50'}
                  onChange={(val) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), ribColor: val } }))}
                />
              </div>
              <div>
                <Slider
                  label="Rib Thickness"
                  value={state.argyle?.ribThickness ?? 1}
                  min={0}
                  max={8}
                  step={0.5}
                  onChange={(v) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), ribThickness: v } }))}
                  unit="px"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <Slider
                  label="Rib Spacing"
                  value={state.argyle?.ribSpacing ?? Math.max(20, state.spacing * 4)}
                  min={8}
                  max={400}
                  step={1}
                  onChange={(v) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), ribSpacing: v } }))}
                  unit="px"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Rib Angle</label>
                <select
                  value={state.argyle?.ribAngle || 'both'}
                  onChange={(e) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), ribAngle: e.target.value as any } }))}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                >
                  <option value="both">Both</option>
                  <option value="diag1">45°</option>
                  <option value="diag2">-45°</option>
                </select>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <input
                id="argyleRibOverlay"
                type="checkbox"
                checked={(state.argyle?.ribOverlay || 'clipped') === 'overlay'}
                onChange={(e) => setState(prev => ({ ...prev, argyle: { ...(prev.argyle || {}), ribOverlay: e.target.checked ? 'overlay' : 'clipped' } }))}
                className="w-4 h-4 text-sky-500 rounded focus:ring-sky-500"
              />
              <label htmlFor="argyleRibOverlay" className="text-sm font-medium text-gray-700">
                Ribs overlay diamonds (stitched) — unchecked = clipped
              </label>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
