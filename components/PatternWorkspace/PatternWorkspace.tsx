import React, { useState, useEffect } from 'react';
import PatternCanvas from './PatternCanvas';
import PatternControls from './PatternControls';
import BackgroundControls from './BackgroundControls';
import ExportModal, { ExportOptions } from './ExportModal';
import { PatternState } from './types';
import { Background } from '../../src/types';
import { drawPattern } from './drawingUtils';

export default function PatternWorkspace() {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [patternState, setPatternState] = useState<PatternState>({
    patternType: 'grid',
    gridCountX: 20,  // Dense grid by default
    gridCountY: 20,  // Dense grid by default
    spacing: 2,      // Minimal spacing
    scale: 100,      // Full scale
    rotation: 0,
    alternate: false,
    seamless: true,  // Seamless by default
    opacity: 1,
    // Argyle defaults are provided but only used when patternType === 'argyle'
    argyle: {
      diamondScale: 80,
      fillMode: 'color',
      primaryColor: '#ffffff',
      secondaryColor: '#c0392b',
      accentColor: '#2c3e50',
      staggered: true,
      outlineEnabled: false,
      outlineColor: '#000000',
      outlineThickness: 1,
      ribEnabled: true,
      ribColor: '#2c3e50',
      ribThickness: 1,
      ribSpacing: 60,
      ribAngle: 'both',
      ribMargin: 4,
      ribOverlay: 'clipped'
    },
    filters: {
      hue: 0,
      saturation: 100,
      brightness: 100,
      contrast: 100
    }
  });
  const [background, setBackground] = useState<Background>({
    type: 'solid',
    color: '#ffffff'
  });
  const [globalRotation, setGlobalRotation] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [showOverlay, setShowOverlay] = useState(true);

  // Keyboard shortcut: toggle overlay with "G" (press G to show/hide debug overlay)
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      // Ignore when typing into inputs
      const target = e.target as HTMLElement | null;
      if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA')) return;
      if (e.key === 'g' || e.key === 'G') {
        setShowOverlay(s => !s);
      }
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, []);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      console.log('handleImageUpload: no file selected');
      return;
    }

    console.log('handleImageUpload: file selected', file.name, file.type, file.size);

    // Always load the chosen file locally (no backend dependency).
    // This uses a FileReader to convert the image to a data URL and avoids CORS/backend issues.
    const reader = new FileReader();
    reader.onload = () => {
      const data = reader.result as string;
      console.log('handleImageUpload: reader loaded, data length', data ? data.length : 0);
      setImageSrc(data);
    };
    reader.onerror = (err) => {
      console.error('handleImageUpload: reader error', err);
    };
    reader.readAsDataURL(file);
  };

  const handleExport = (options: ExportOptions) => {
    if (!imageSrc) return;

    const exportCanvas = document.createElement('canvas');
    const exportCtx = exportCanvas.getContext('2d');

    exportCanvas.width = options.width;
    exportCanvas.height = options.height;

    if (exportCtx) {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          // Export scaling: match the visual layout from the preview by scaling spacing and
          // relying on the larger export canvas size for element sizing. Do NOT multiply zoom
          // by export scale (that caused double-scaling and mismatched spacing).
          const exportScale = options.width / 800;

          // Create a derived pattern state for export where spacing is scaled to the export resolution.
          const exportPatternState = {
            ...patternState,
            spacing: Math.max(0, Math.round(patternState.spacing * exportScale))
          };

          // Render export without the debug overlay (showOverlay = false). Keep the same zoom value.
          drawPattern(exportCtx, img, exportPatternState, background, globalRotation, zoom, options.width, options.height, false);

          const dataUrl = exportCanvas.toDataURL(`image/${options.format}`, options.format === 'jpeg' ? options.quality / 100 : undefined);
          
          const link = document.createElement('a');
          link.download = `pattern.${options.format}`;
          link.href = dataUrl;
          link.click();
        };
        img.src = imageSrc;
      }
    };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-4">
      <div className="max-w-7xl mx-auto">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">Pattern Maker</h1>
          <p className="text-gray-600">Upload an image and create patterns for print-on-demand items</p>
        </header>
        
        <div className="flex gap-6">
          {/* Controls Panel */}
          <aside className="w-[380px] space-y-6">
            {/* Upload Section */}
            <div className="bg-white/70 backdrop-blur-xl border border-white/30 rounded-2xl p-6 shadow-2xl">
              <h3 className="font-semibold text-gray-700 mb-4">Upload Image</h3>
              <input 
                type="file" 
                accept="image/*" 
                onChange={handleImageUpload}
                className="w-full p-3 border border-gray-300 rounded-lg bg-white/50 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              {imageSrc && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-700">✅ Image uploaded! Configure your pattern below.</p>
                </div>
              )}
            </div>

            <PatternControls 
              state={patternState} 
              setState={setPatternState}
              disabled={false}
            />
          </aside>

          {/* Canvas Area */}
          <main className="flex-1 space-y-6">
            <PatternCanvas
              imageSrc={imageSrc}
              patternState={patternState}
              background={background}
              globalRotation={globalRotation}
              zoom={zoom}
              showOverlay={showOverlay}
            />
            
            {/* Background and Canvas Controls - Positioned directly under canvas */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <BackgroundControls 
                background={background} 
                setBackground={setBackground} 
              />

              {/* Canvas Controls */}
              <div className="bg-white/70 backdrop-blur-xl border border-white/30 rounded-2xl p-6 shadow-2xl">
                <h3 className="font-semibold text-gray-700 mb-4">Canvas Controls</h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 block mb-2">
                      Global Zoom: {zoom.toFixed(2)}x
                    </label>
                    <input 
                      type="range" 
                      min="0.2" 
                      max="3" 
                      step="0.1" 
                      value={zoom}
                      onChange={(e) => setZoom(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700 block mb-2">
                      Global Rotation: {globalRotation}°
                    </label>
                    <input 
                      type="range" 
                      min="0" 
                      max="360" 
                      step="1" 
                      value={globalRotation}
                      onChange={(e) => setGlobalRotation(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  <div className="flex items-center gap-3">
                    <input
                      id="debugOverlay"
                      type="checkbox"
                      checked={showOverlay}
                      onChange={(e) => setShowOverlay(e.target.checked)}
                      className="w-4 h-4 text-sky-500 rounded focus:ring-sky-500"
                    />
                    <label htmlFor="debugOverlay" className="text-sm font-medium text-gray-700">
                      Show debug overlay (G)
                    </label>
                  </div>
                  <button 
                    onClick={() => setIsExportModalOpen(true)}
                    disabled={!imageSrc}
                    className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded-lg transition-colors"
                  >
                    Export Pattern
                  </button>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      
      <ExportModal 
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        onExport={handleExport}
        patternState={patternState}
        background={background}
        imageSrc={imageSrc}
        globalRotation={globalRotation}
        zoom={zoom}
      />
    </div>
  );
}
