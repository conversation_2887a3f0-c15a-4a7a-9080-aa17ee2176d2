export interface PatternState {
  patternType: 'grid' | 'diamond' | 'checkerboard' | 'argyle';
  gridCountX: number;
  gridCountY: number;
  spacing: number;
  scale: number;
  rotation: number;
  alternate: boolean;
  seamless: boolean;
  opacity: number;
  
  // Argyle-specific settings
  argyle?: {
    diamondScale?: number;       // relative scale of diamonds (0-200 where 100 = full cell)
    fillMode?: 'color' | 'image';// fill diamonds with solid color or the uploaded image
    primaryColor?: string;       // main diamond color
    secondaryColor?: string;     // staggered/offset diamond color
    accentColor?: string;        // optional accent for ribs/outlines
    staggered?: boolean;         // render offset diamond layer
    outlineEnabled?: boolean;
    outlineColor?: string;
    outlineThickness?: number;
    ribEnabled?: boolean;
    ribColor?: string;
    ribThickness?: number;
    ribSpacing?: number;         // spacing between rib lines (px)
    ribAngle?: 'both' | 'diag1' | 'diag2';
    ribMargin?: number;          // gap from diamond edge
    ribOverlay?: 'clipped' | 'overlay'; // clipped = lines stop before diamonds, overlay = lines drawn on top
    ribPreviewOnly?: boolean;    // if true, ribs are shown only in preview (not included in exported images)
    preset?: string;             // selected preset key (e.g., 'classic', 'muted', 'neon', 'monochrome')
  };

  filters: {
    hue: number;
    saturation: number;
    brightness: number;
    contrast: number;
  };

  // New: visual grid lines that run between tiles (don't cross images)
  gridLines?: {
    enabled: boolean;
    color: string;        // hex color when not linked to image
    thickness: number;    // base thickness in pixels
    margin: number;       // extra gap between image edge and line (in px)
    linkColorToImage: boolean; // when true, line color is derived from image filters
  };
}

export interface Background {
  type: 'solid' | 'gradient' | 'transparent';
  color?: string;
  startColor?: string;
  endColor?: string;
  angle?: number;
}
