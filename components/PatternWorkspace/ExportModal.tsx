import React, { useState, useEffect, useRef } from 'react';
import { PatternState, Background } from './types';
import { drawPattern } from './drawingUtils';

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: ExportOptions) => void;
  patternState: PatternState;
  background: Background;
  imageSrc: string | null;
  globalRotation: number;
  zoom: number;
}

export interface ExportOptions {
  format: 'png' | 'jpeg';
  width: number;
  height: number;
  dpi: number;
  quality: number; // 0-100 for JPEG
  filename?: string; // optional filename (without extension)
}

const ExportModal: React.FC<ExportModalProps> = ({ 
  isOpen, 
  onClose, 
  onExport, 
  patternState, 
  background, 
  imageSrc, 
  globalRotation, 
  zoom 
}) => {
  const [format, setFormat] = useState<'png' | 'jpeg'>('png');
  const [preset, setPreset] = useState<string>('custom');
  const [width, setWidth] = useState(3000);
  const [height, setHeight] = useState(3000);
  const [dpi, setDpi] = useState(300);
  const [quality, setQuality] = useState(95);
  const [filename, setFilename] = useState<string>('pattern');
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  const presets = {
    'custom': { width: 3000, height: 3000, label: 'Custom' },
    'tshirt': { width: 4500, height: 5400, label: 'T-Shirt (15" x 18")' },
    'poster_small': { width: 3600, height: 2400, label: 'Poster 12" x 8"' },
    'poster_large': { width: 7200, height: 4800, label: 'Poster 24" x 16"' },
    'phone_case': { width: 1800, height: 3000, label: 'Phone Case' },
    'tote_bag': { width: 4200, height: 4200, label: 'Tote Bag 14" x 14"' },
    'mug_wrap': { width: 2475, height: 1155, label: 'Mug Wrap 8.25" x 3.85"' },
    'pillow': { width: 4800, height: 4800, label: 'Pillow 16" x 16"' },
    'canvas_print': { width: 7200, height: 7200, label: 'Canvas Print 24" x 24"' }
  };

  const handlePresetChange = (presetKey: string) => {
    setPreset(presetKey);
    if (presetKey !== 'custom') {
      const presetData = presets[presetKey as keyof typeof presets];
      setWidth(presetData.width);
      setHeight(presetData.height);
    }
  };

  const handleExport = () => {
    onExport({ format, width, height, dpi, quality, filename });
    onClose();
  };

  const estimatedFileSize = () => {
    const pixels = width * height;
    if (format === 'png') return `~${(pixels * 4 / 1024 / 1024).toFixed(1)}MB`;
    if (format === 'jpeg') return `~${(pixels * 3 * (quality / 100) / 1024 / 1024).toFixed(1)}MB`;
    return '';
  };

  useEffect(() => {
    if (isOpen && imageSrc && previewCanvasRef.current) {
      const canvas = previewCanvasRef.current;
      const ctx = canvas.getContext('2d');
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        const previewSize = 400;
        const exportAspectRatio = width / height;
        canvas.width = exportAspectRatio >= 1 ? previewSize : previewSize * exportAspectRatio;
        canvas.height = exportAspectRatio < 1 ? previewSize : previewSize / exportAspectRatio;
        
        if (ctx) {
          const exportZoomFactor = canvas.width / width;
          drawPattern(ctx, img, patternState, background, globalRotation, zoom * exportZoomFactor, canvas.width, canvas.height);
        }
      };
      img.src = imageSrc;
    }
  }, [isOpen, imageSrc, patternState, background, globalRotation, zoom, width, height]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-white rounded-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Export Pattern</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 text-2xl font-bold">×</button>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center aspect-square">
            <canvas ref={previewCanvasRef} className="max-w-full max-h-full rounded-md shadow-inner" />
          </div>

          <div className="space-y-4 overflow-y-auto max-h-[70vh] pr-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Filename</label>
              <input
                type="text"
                value={filename}
                onChange={(e) => setFilename(e.target.value)}
                className="w-full p-2 mb-3 border border-gray-300 rounded-lg focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="pattern-name"
              />
              <label className="block text-sm font-medium text-gray-700 mb-2">File Format</label>
              <div className="grid grid-cols-2 gap-2">
                {['png', 'jpeg'].map(f => (
                  <button 
                    key={f}
                    onClick={() => setFormat(f as 'png' | 'jpeg')}
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${ 
                      format === f ? 'bg-sky-500 text-white shadow-sm' : 'bg-gray-200 hover:bg-gray-300'
                    }`}>
                    {f.toUpperCase()}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Size Preset</label>
              <select
                value={preset}
                onChange={(e) => handlePresetChange(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                {Object.entries(presets).map(([key, { label }]) => (
                  <option key={key} value={key}>{label}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Width (px)</label>
                <input
                  type="number"
                  value={width}
                  onChange={(e) => setWidth(Number(e.target.value))}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Height (px)</label>
                <input
                  type="number"
                  value={height}
                  onChange={(e) => setHeight(Number(e.target.value))}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">DPI: {dpi}</label>
              <input type="range" min="72" max="600" value={dpi} onChange={(e) => setDpi(Number(e.target.value))} className="w-full" />
            </div>

            {format === 'jpeg' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Quality: {quality}%</label>
                <input type="range" min="10" max="100" value={quality} onChange={(e) => setQuality(Number(e.target.value))} className="w-full" />
              </div>
            )}

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 text-xs">
              <div className="grid grid-cols-2 gap-2 text-gray-600">
                <div><span className="font-medium">Dimensions:</span> {width}×{height}px</div>
                <div><span className="font-medium">Print Size:</span> {(width / dpi).toFixed(1)}" × {(height / dpi).toFixed(1)}"</div>
                <div><span className="font-medium">Est. Size:</span> {estimatedFileSize()}</div>
                <div><span className="font-medium">Best For:</span> {dpi >= 300 ? 'Print' : 'Web'}</div>
              </div>
            </div>

            <div className="flex gap-3 pt-2 border-t border-gray-200">
              <button onClick={onClose} className="flex-1 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50">Cancel</button>
              <button onClick={handleExport} className="flex-1 px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium">Export</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportModal;
