import React from 'react';

interface HubScreenProps {
  onStartProject: (mode: 'pattern' | 'crest' | 'text' | 'video') => void;
}

const HubScreen: React.FC<HubScreenProps> = ({ onStartProject }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex flex-col items-center justify-center p-8">
      <div className="text-center mb-12">
        <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Imogen</h1>
        <p className="text-xl text-gray-600 mt-2">Your AI-powered creative studio</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-4xl">
        <div 
          className="bg-white p-8 rounded-lg shadow-lg cursor-pointer transform hover:scale-105 transition-transform duration-300"
          onClick={() => onStartProject('pattern')}
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Pattern Maker</h2>
          <p className="text-gray-600">Create seamless patterns from images or procedural generation.</p>
        </div>

        <div 
          className="bg-white p-8 rounded-lg shadow-lg cursor-pointer transform hover:scale-105 transition-transform duration-300"
          onClick={() => onStartProject('crest')}
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Graphic Designer</h2>
          <p className="text-gray-600">Design logos, crests, and other vector graphics with AI.</p>
        </div>

        <div 
          className="bg-white p-8 rounded-lg shadow-lg cursor-pointer transform hover:scale-105 transition-transform duration-300"
          onClick={() => onStartProject('text')}
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Text Designer</h2>
          <p className="text-gray-600">Create beautiful text-based designs and lockups.</p>
        </div>

        <div 
          className="bg-white p-8 rounded-lg shadow-lg cursor-pointer transform hover:scale-105 transition-transform duration-300"
          onClick={() => onStartProject('video')}
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Video Generator</h2>
          <p className="text-gray-600">Generate stunning videos from a simple text prompt.</p>
        </div>
      </div>
    </div>
  );
};

export default HubScreen;
