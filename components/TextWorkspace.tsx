import React, { useState } from 'react';
import { TextDesignConfig } from '../src/types';
import TextRenderer from './shared/TextRenderer';
import TextControls from './shared/TextControls';

// Re-using the same UI components from the modal for consistency
const fonts = [
  { name: 'Bold Impact', value: 'Impact, Arial Black, sans-serif' },
  { name: 'Clean Sans', value: 'Helvetica, Arial, sans-serif' },
  { name: 'Classic Serif', value: 'Georgia, Times, serif' },
  { name: 'Vintage Script', value: 'Brush Script MT, cursive' },
  { name: 'Modern Thin', value: 'Helvetica Neue, Arial, sans-serif' },
  { name: 'Retro Bold', value: 'Arial Black, Gadget, sans-serif' },
];

const styles = [
  { name: 'Simple', value: 'simple', description: 'Clean text only' },
  { name: 'Outlined', value: 'outlined', description: 'Text with outline' },
  { name: 'Shadow', value: 'shadow', description: 'Text with drop shadow' },
  { name: 'Vintage', value: 'vintage', description: 'Retro style effect' },
  { name: 'Modern', value: 'modern', description: 'Contemporary look' },
];

const shapes = [
  { name: 'No Shape', value: 'none' },
  { name: 'Circle', value: 'circle' },
  { name: 'Rectangle', value: 'rectangle' },
  { name: 'Banner', value: 'banner' },
];


interface TextWorkspaceProps {
    onGenerate: (config: TextDesignConfig) => void;
    // We might need a way to go back to the hub
    onBack: () => void; 
}

export const TextWorkspace: React.FC<TextWorkspaceProps> = ({ onGenerate, onBack }) => {
  const [config, setConfig] = useState<TextDesignConfig>({
    text: 'YOUR TEXT HERE',
    fontFamily: fonts[0].value,
    fontSize: 48,
    color: '#000000',
    backgroundColor: 'transparent',
    style: 'simple',
    shape: 'none',
    isCircular: false,
    circularRadius: 120,
    circularStartAngle: 0,
    circularDirection: 'clockwise',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onGenerate(config);
  };


  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 p-4 md:p-8">
        <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold text-gray-800">Text Design Workspace</h1>
                <button
                    onClick={onBack}
                    className="px-6 py-2 text-gray-600 bg-white rounded-md shadow-sm hover:bg-gray-50 transition-colors"
                >
                    ← Back to Hub
                </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Live Preview */}
                <div className="bg-white rounded-lg shadow-lg p-6 text-center min-h-[400px] flex items-center justify-center">
                  <svg width="300" height="300" viewBox="0 0 300 300" className="max-w-full max-h-full">
                    {config.shape !== 'none' && config.backgroundColor !== 'transparent' && (
                      <>
                        {config.shape === 'circle' && <circle cx="150" cy="150" r="130" fill={config.backgroundColor} />}
                        {config.shape === 'rectangle' && <rect x="20" y="100" width="260" height="100" fill={config.backgroundColor} rx="8" />}
                        {config.shape === 'banner' && <rect x="20" y="120" width="260" height="60" fill={config.backgroundColor} stroke="#333" strokeWidth="2" />}
                      </>
                    )}
                    <TextRenderer
                      id="text-preview"
                      text={config.text || 'YOUR TEXT'}
                      fontFamily={config.fontFamily}
                      fontSize={config.fontSize * 0.4}
                      color={config.color}
                      style={config.style}
                      isCircular={config.isCircular}
                      circularRadius={config.circularRadius}
                      circularStartAngle={config.circularStartAngle}
                      circularDirection={config.circularDirection}
                      x={150}
                      y={160}
                      anchor="middle"
                      viewBoxSize={300}
                    />
                  </svg>
                </div>

                {/* Controls */}
                <div className="space-y-4">
                  <TextControls value={config} onChange={setConfig} />
                </div>
              </div>

              <div className="flex justify-end gap-4 pt-6 border-t">
                <button
                  type="submit"
                  className="px-8 py-3 bg-sky-500 text-white rounded-lg hover:bg-sky-600 font-bold text-lg"
                >
                  Generate Image from Text
                </button>
              </div>
            </form>
        </div>
    </div>
  );
};

export default TextWorkspace;
