import { GoogleGenerativeAI } from "@google/generative-ai";

// Initialize the Google Generative AI client with API key from environment
const genAI = new GoogleGenerativeAI(import.meta.env.VITE_GOOGLE_API_KEY || "");

/**
 * Generate an image from a text prompt using Veo 3
 * @param prompt - The text description of the image
 * @returns A base64 image string
 */
export async function generateImageFromText(prompt: string): Promise<string | null> {
  try {
    const imageModel = genAI.getGenerativeModel({ model: "imagen-3.0" });

    const result = await imageModel.generateContent(prompt);

    // Extract base64 image data if available
    const part = result?.response?.candidates?.[0]?.content?.parts?.find(
      (p: any) => p.inlineData?.mimeType?.startsWith("image/")
    );
    const imageBase64 = part?.inlineData?.data;
    return imageBase64 || null;
  } catch (error) {
    console.error("Error generating image with Veo 3:", error);
    return null;
  }
}
