// Video Generation Service
// This service will handle communication with the Python backend for video generation.

export interface VideoGenerationResponse {
  message: string;
  job_id: string;
  status_url: string;
}

export interface VideoStatusResponse {
  status: 'pending' | 'processing' | 'running' | 'completed' | 'failed';
  video_url?: string;
  error?: string;
}

export interface VideoJob {
  job_id: string;
  status: 'pending' | 'processing' | 'running' | 'completed' | 'failed';
  prompt: string;
  status_url: string;
  video_url?: string;
  error?: string;
}

export interface VideoGenerationParams {
  durationSeconds?: number;
  aspectRatio?: string;
  generateAudio?: boolean;
  resolution?: string;
  personGeneration?: string;
  sampleCount?: number;
}

export interface VideoGenerationProvider {
  name: string;
  generateVideo(prompt: string, image?: File, params?: VideoGenerationParams): Promise<VideoGenerationResponse>;
  getVideoStatus(statusUrl: string): Promise<VideoStatusResponse>;
}

// This provider will call our local Python server
export class PythonVideoProvider implements VideoGenerationProvider {
  name = 'Python Video Generation';
  private backendUrl = 'http://127.0.0.1:5000'; // Default URL for the local Python server

  async generateVideo(prompt: string, image?: File, params?: VideoGenerationParams): Promise<VideoGenerationResponse> {
    console.log(`[PythonVideoProvider] Sending prompt to backend: "${prompt}"`);
    try {
      // Use FormData to support file uploads
      const formData = new FormData();
      formData.append('prompt', prompt);

      // Add parameters as JSON string
      if (params) {
        formData.append('params', JSON.stringify(params));
      }

      // Add image if provided
      if (image) {
        formData.append('image', image);
      }

      const response = await fetch(`${this.backendUrl}/generate-video`, {
        method: 'POST',
        body: formData, // Don't set Content-Type header, let browser set it with boundary
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Backend API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      if (data.job_id) {
        console.log('✅ Video generation started successfully!', data);
        return data;
      }

      throw new Error('Invalid response from backend API');
    } catch (error) {
      console.error('Failed to communicate with the video generation backend:', error);
      throw new Error('The video generation backend is not available. Please ensure it is running.');
    }
  }

  async getVideoStatus(statusUrl: string): Promise<VideoStatusResponse> {
    try {
      const response = await fetch(statusUrl);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch video status: ${response.status} ${response.statusText} - ${errorText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to get video status:', error);
      throw new Error('Could not retrieve video status from the backend.');
    }
  }
}

// Service Manager
export class VideoGenerationService {
  private provider: VideoGenerationProvider;

  constructor(provider: VideoGenerationProvider = new PythonVideoProvider()) {
    this.provider = provider;
  }

  setProvider(provider: VideoGenerationProvider) {
    this.provider = provider;
    console.log(`Switched to ${provider.name} for video generation`);
  }

  async generateVideo(prompt: string, image?: File, params?: VideoGenerationParams): Promise<VideoGenerationResponse> {
    console.log(`Generating video with ${this.provider.name}: "${prompt}"`);
    return this.provider.generateVideo(prompt, image, params);
  }

  async getVideoStatus(statusUrl: string): Promise<VideoStatusResponse> {
    return this.provider.getVideoStatus(statusUrl);
  }
}

// Export singleton instance
export const videoService = new VideoGenerationService();

// Convenience functions for easier usage
export const startVideoGeneration = (prompt: string, image?: File, params?: VideoGenerationParams): Promise<VideoGenerationResponse> => {
  return videoService.generateVideo(prompt, image, params);
};

export const getVideoJobStatus = (statusUrl: string): Promise<VideoStatusResponse> => {
  return videoService.getVideoStatus(statusUrl);
};
