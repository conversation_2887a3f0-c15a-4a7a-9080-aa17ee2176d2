// Video Generation Service
// This service will handle communication with the Python backend for video generation.

export interface VideoGenerationResponse {
  message: string;
  job_id: string;
  status_url: string;
}

export interface VideoStatusResponse {
  status: 'pending' | 'completed' | 'failed';
  video_url?: string;
  error?: string;
}

export interface VideoGenerationProvider {
  name: string;
  generateVideo(prompt: string): Promise<VideoGenerationResponse>;
  getVideoStatus(statusUrl: string): Promise<VideoStatusResponse>;
}

// This provider will call our local Python server
export class PythonVideoProvider implements VideoGenerationProvider {
  name = 'Python Video Generation';
  private backendUrl = 'http://127.0.0.1:5000'; // Default URL for the local Python server

  async generateVideo(prompt: string): Promise<VideoGenerationResponse> {
    console.log(`[PythonVideoProvider] Sending prompt to backend: "${prompt}"`);
    try {
      const response = await fetch(`${this.backendUrl}/generate-video`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Backend API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      if (data.job_id) {
        console.log('✅ Video generation started successfully!', data);
        return data;
      }

      throw new Error('Invalid response from backend API');
    } catch (error) {
      console.error('Failed to communicate with the video generation backend:', error);
      throw new Error('The video generation backend is not available. Please ensure it is running.');
    }
  }

  async getVideoStatus(statusUrl: string): Promise<VideoStatusResponse> {
    try {
      const response = await fetch(statusUrl);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch video status: ${response.status} ${response.statusText} - ${errorText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to get video status:', error);
      throw new Error('Could not retrieve video status from the backend.');
    }
  }
}

// Service Manager
export class VideoGenerationService {
  private provider: VideoGenerationProvider;

  constructor(provider: VideoGenerationProvider = new PythonVideoProvider()) {
    this.provider = provider;
  }

  setProvider(provider: VideoGenerationProvider) {
    this.provider = provider;
    console.log(`Switched to ${provider.name} for video generation`);
  }

  async generateVideo(prompt: string): Promise<VideoGenerationResponse> {
    console.log(`Generating video with ${this.provider.name}: "${prompt}"`);
    return this.provider.generateVideo(prompt);
  }

  async getVideoStatus(statusUrl: string): Promise<VideoStatusResponse> {
    return this.provider.getVideoStatus(statusUrl);
  }
}

// Export singleton instance
export const videoService = new VideoGenerationService();
