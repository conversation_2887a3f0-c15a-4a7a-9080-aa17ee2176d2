import React from 'react';
import { DiamondIcon, TextIcon, CrestIcon, UploadIcon } from '../components/icons';

export interface ToolInfo {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  route: string;
  isActive: boolean;
}

export const availableTools: ToolInfo[] = [
  {
    id: 'pattern',
    title: 'Pattern Maker',
    description: 'Generate a tileable element and design a seamless repeating pattern.',
    icon: React.createElement(DiamondIcon, { className: 'w-8 h-8' }),
    route: '/pattern',
    isActive: true
  },
  {
    id: 'text',
    title: 'Text Designer', 
    description: 'Type your text and create print-ready designs for POD clothing.',
    icon: React.createElement(TextIcon, { className: 'w-8 h-8' }),
    route: '/text',
    isActive: true
  },
  {
    id: 'crest',
    title: 'Crest Creator',
    description: 'Design a classic circular crest or logo with custom text.',
    icon: React.createElement(CrestIcon, { className: 'w-8 h-8' }),
    route: '/crest', 
    isActive: true
  },
  {
    id: 'image',
    title: 'Image Processor',
    description: 'Upload your own image to use as a starting point for any project.',
    icon: React.createElement(UploadIcon, { className: 'w-8 h-8' }),
    route: '/image',
    isActive: true
  }
];

export const getToolById = (id: string): ToolInfo | undefined => {
  return availableTools.find(tool => tool.id === id);
};

export const getActiveTools = (): ToolInfo[] => {
  return availableTools.filter(tool => tool.isActive);
}; 