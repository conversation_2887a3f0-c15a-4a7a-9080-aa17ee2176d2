<template>
  <div class="video-studio-container">
    <h1 class="title">Video Studio</h1>
    <p class="subtitle">Create stunning 8-second videos with audio from a simple text prompt, with or without a reference image.</p>

    <div class="generator-card shadow-box">
      <textarea
        v-model="prompt"
        class="prompt-input"
        placeholder="e.g., A majestic lion roaring on a mountain top at sunrise, cinematic quality"
      ></textarea>
      <div class="controls">
        <button @click="triggerImageUpload" class="control-btn">
          <i class="fas fa-image"></i> {{ imageName || 'Attach Image' }}
        </button>
        <input
          type="file"
          ref="imageUpload"
          @change="handleImageChange"
          accept="image/*"
          hidden
        />
        <button @click="handleGenerate" class="generate-btn" :disabled="isGenerating">
          <i class="fas fa-magic"></i> {{ isGenerating ? 'Generating...' : 'Generate Video' }}
        </button>
      </div>
    </div>

    <div v-if="jobs.length > 0" class="jobs-section">
      <div v-for="job in jobs" :key="job.job_id" class="job-card shadow-box">
        <div class="job-header">
          <p class="job-prompt"><strong>Prompt:</strong> {{ job.prompt }}</p>
          <span :class="['status-badge', job.status]">{{ job.status }}</span>
        </div>
        <div class="job-content">
          <div v-if="job.status === 'processing' || job.status === 'running'" class="spinner-container">
            <div class="spinner"></div>
            <p>Your video is being created. This can take a few minutes...</p>
          </div>
          <div v-else-if="job.status === 'completed' && job.video_url" class="video-container">
            <video :src="`http://localhost:5000${job.video_url}`" controls autoplay muted loop></video>
          </div>
          <div v-else-if="job.status === 'failed'" class="error-container">
            <p><strong>Error:</strong> {{ job.error }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { startVideoGeneration, getVideoJobStatus, VideoJob } from '../services/videoGenerationService';

const prompt = ref('');
const imageFile = ref<File | null>(null);
const imageName = ref('');
const imageUpload = ref<HTMLInputElement | null>(null);
const isGenerating = ref(false);

const jobs = ref<VideoJob[]>([]);
const pollingIntervals = new Map<string, number>();

const triggerImageUpload = () => {
  imageUpload.value?.click();
};

const handleImageChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    imageFile.value = target.files[0];
    imageName.value = target.files[0].name;
  }
};

const handleGenerate = async () => {
  if (!prompt.value.trim()) {
    alert('Please enter a prompt.');
    return;
  }

  isGenerating.value = true;
  try {
    const initialJob = await startVideoGeneration(prompt.value, imageFile.value || undefined);
    
    const newJob: VideoJob = {
      job_id: initialJob.job_id,
      status: 'processing',
      prompt: prompt.value,
      status_url: initialJob.status_url,
    };
    jobs.value.unshift(newJob); // Add to the top of the list

    startPolling(initialJob.job_id);

    // Clear inputs for next generation
    prompt.value = '';
    imageFile.value = null;
    imageName.value = '';
    if (imageUpload.value) imageUpload.value.value = '';

  } catch (error) {
    console.error('Generation failed to start:', error);
    alert((error as Error).message);
  } finally {
    isGenerating.value = false;
  }
};

const startPolling = (jobId: string) => {
  const intervalId = window.setInterval(async () => {
    try {
      const updatedJob = await getVideoJobStatus(jobId);
      const jobIndex = jobs.value.findIndex(j => j.job_id === jobId);
      if (jobIndex !== -1) {
        jobs.value[jobIndex] = { ...jobs.value[jobIndex], ...updatedJob };
      }

      if (updatedJob.status === 'completed' || updatedJob.status === 'failed') {
        stopPolling(jobId);
      }
    } catch (error) {
      console.error(`Failed to poll status for job ${jobId}:`, error);
      stopPolling(jobId);
    }
  }, 5000); // Poll every 5 seconds

  pollingIntervals.set(jobId, intervalId);
};

const stopPolling = (jobId: string) => {
  if (pollingIntervals.has(jobId)) {
    clearInterval(pollingIntervals.get(jobId));
    pollingIntervals.delete(jobId);
  }
};
</script>

<style scoped>
.video-studio-container {
  padding: 2rem;
  background: linear-gradient(to bottom right, #fdfbfb, #ebedee);
  min-height: 100vh;
  font-family: 'Inter', sans-serif;
}

.title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.shadow-box {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 4px 10px rgba(0, 0, 0, 0.02);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.generator-card {
  max-width: 800px;
  margin: 0 auto 2rem auto;
}

.prompt-input {
  width: 100%;
  min-height: 100px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1rem;
  margin-bottom: 1rem;
  resize: vertical;
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-btn, .generate-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.control-btn {
  background-color: #f0f2f5;
  color: #555;
}

.control-btn:hover {
  background-color: #e4e6eb;
}

.generate-btn {
  background-color: #4a90e2;
  color: white;
}

.generate-btn:hover:not(:disabled) {
  background-color: #357abd;
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.generate-btn:disabled {
  background-color: #a0c3e8;
  cursor: not-allowed;
}

.jobs-section {
  max-width: 800px;
  margin: 2rem auto 0 auto;
}

.job-card {
  margin-bottom: 1.5rem;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.job-prompt {
  color: #333;
  margin: 0;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.processing, .status-badge.running { background-color: #fffbe6; color: #f59e0b; }
.status-badge.completed { background-color: #f0fdf4; color: #22c55e; }
.status-badge.failed { background-color: #fef2f2; color: #ef4444; }

.video-container video {
  width: 100%;
  border-radius: 8px;
}

.spinner-container {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #4a90e2;
  animation: spin 1s ease infinite;
  margin: 0 auto 1rem auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  color: #ef4444;
  background-color: #fef2f2;
  padding: 1rem;
  border-radius: 8px;
}
</style>