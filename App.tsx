import React, { useState, useRef, useCallback, useEffect } from 'react';
import ControlPanel from './components/ControlPanel';
import PatternCanvas from './components/PatternCanvas';
import { GraphicDesignerWorkspace } from './components/GraphicDesignerWorkspace';
import { TextWorkspace } from './components/TextWorkspace';
import PatternWorkspace from './components/PatternWorkspace/PatternWorkspace';
import VideoWorkspace from './components/VideoWorkspace/VideoWorkspace';
import HubScreen from './components/HubScreen';
import { imageService } from './services/imageGenerationService';
import { videoService } from './services/videoGenerationService';
import { removeBackgroundFromImage } from './services/geminiService';
import html2canvas from 'html2canvas';
import { 
    ProjectMode, 
    WorkflowStep, 
    Filters, 
    ImageLayer, 
    ProceduralLayer, 
    Layer,
    Background,
    TextDesignConfig
} from './src/types';

const defaultFilters: Filters = {
    brightness: 100,
    contrast: 100,
    sepia: 0,
    hueRotate: 0,
    invert: 0,
    finish: 0,
};

const createNewImageLayer = (name: string): ImageLayer => ({
    id: `layer-${Date.now()}`,
    type: 'image',
    name,
    image: null,
    scale: 50,
    rotation: 0,
    opacity: 1,
    gridCountX: 5,
    gridCountY: 5,
    patternType: 'grid',
    filters: { ...defaultFilters },
    mode: 'single',
    spacing: 0,
    offset: 0,
    alternatePattern: false,
});

const createNewProceduralLayer = (name: string): ProceduralLayer => ({
    id: `layer-${Date.now()}`,
    type: 'procedural',
    name,
    opacity: 1,
    proceduralType: 'dots',
    config: {
        dotColor: '#3b82f6',
        dotSize: 10,
        dotSpacing: 40,
        stripeColor: '#3b82f6',
        stripeWidth: 10,
        stripeSpacing: 40,
        stripeAngle: 45,
        plaidColor1: '#3b82f6',
        plaidColor2: '#ef4444',
        plaidColor3: '#10b981',
        plaidWidth: 20,
        plaidSpacing: 40,
    }
});

const App: React.FC = () => {
    // --- App Flow State ---
    const [projectMode, setProjectMode] = useState<ProjectMode>('hub');
    const [workflowStep, setWorkflowStep] = useState<WorkflowStep>('generate');

    // --- Core State ---
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isProcessingImage, setIsProcessingImage] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [prompt, setPrompt] = useState<string>('a simple styled icon of a bicycle');
    const [videoUrl, setVideoUrl] = useState<string | null>(null);
    const [isVideoLoading, setIsVideoLoading] = useState<boolean>(false);
    
    // --- Layer State ---
    const [layers, setLayers] = useState<Layer[]>([]);
    const [selectedLayerId, setSelectedLayerId] = useState<string | null>(null);
    
    // --- Background & Global State ---
    const [background, setBackground] = useState<Background>({ type: 'solid', color: '#FFFFFF' });
    const [globalRotation, setGlobalRotation] = useState<number>(0);
    const [zoom, setZoom] = useState<number>(1);
    
    const canvasRef = useRef<HTMLDivElement>(null);
    const selectedLayer = layers.find(l => l.id === selectedLayerId);

    useEffect(() => {
        if (!selectedLayerId && layers.length > 0) {
            setSelectedLayerId(layers[0].id);
        }
    }, [layers, selectedLayerId]);

    const resetProjectState = () => {
        setLayers([]);
        setSelectedLayerId(null);
        setBackground({ type: 'solid', color: '#FFFFFF' });
        setGlobalRotation(0);
        setZoom(1);
        setError(null);
        setPrompt('a simple styled icon of a bicycle');
        setIsLoading(false);
        setIsProcessingImage(false);
    };

    const handleStartProject = (mode: ProjectMode) => {
        resetProjectState();
        setProjectMode(mode);

        if (mode === 'text') {
            setWorkflowStep('design');
        } else if (mode === 'pattern') {
            // Pattern mode uses the PatternWorkspace component directly
            setWorkflowStep('design');
        } else if (['crest', 'image'].includes(mode)) {
            const firstLayer = createNewImageLayer('Layer 1');
            setLayers([firstLayer]);
            setSelectedLayerId(firstLayer.id);
            setWorkflowStep('generate');
        }
    };

    const handleNewProject = () => {
        setProjectMode('hub');
        resetProjectState();
    };

    const updateLayer = (id: string, newProps: Partial<Layer>) => {
        setLayers(prevLayers =>
            prevLayers.map(l =>
                l.id === id ? { ...l, ...newProps } as Layer : l
            )
        );
    };

    const addImageLayer = () => {
        const newLayer = createNewImageLayer(`Layer ${layers.length + 1}`);
        setLayers([...layers, newLayer]);
        setSelectedLayerId(newLayer.id);
    };

    const addProceduralLayer = () => {
        const newLayer = createNewProceduralLayer(`Layer ${layers.length + 1}`);
        setLayers([...layers, newLayer]);
        setSelectedLayerId(newLayer.id);
    };

    const deleteLayer = (id: string) => {
        setLayers(layers.filter(l => l.id !== id));
        if (selectedLayerId === id) {
            setSelectedLayerId(layers.length > 1 ? layers.filter(l => l.id !== id)[0].id : null);
        }
    };

    const handleGenerateImage = useCallback(async () => {
        if (!prompt) {
            setError("Please enter a prompt.");
            return;
        }
        if (!selectedLayerId || selectedLayer?.type !== 'image') {
            setError("Please select an image layer to generate an image for.");
            return;
        }

        setIsLoading(true);
        setError(null);
        try {
            const imageUrl = await imageService.generateImage(prompt);
            updateLayer(selectedLayerId, { image: imageUrl });
        } catch (e) {
            setError(e instanceof Error ? e.message : "Image generation failed. Make sure the backend is running.");
        } finally {
            setIsLoading(false);
        }
    }, [prompt, selectedLayerId, selectedLayer]);

    const handleGenerateVideo = useCallback(async () => {
        if (!prompt) {
            setError("Please enter a prompt for the video.");
            return;
        }

        setIsVideoLoading(true);
        setError(null);
        setVideoUrl(null);

        try {
            const backendUrl = 'http://127.0.0.1:5000';
            const initialResponse = await videoService.generateVideo(prompt);
            const { job_id, status_url } = initialResponse;

            // Poll for the video status
            const poll = async () => {
                try {
                    const statusResponse = await videoService.getVideoStatus(backendUrl + status_url);
                    if (statusResponse.status === 'completed') {
                        setVideoUrl(backendUrl + statusResponse.video_url);
                        setIsVideoLoading(false);
                    } else if (statusResponse.status === 'failed') {
                        setError(statusResponse.error || 'Video generation failed.');
                        setIsVideoLoading(false);
                    } else {
                        setTimeout(poll, 3000); // Poll every 3 seconds
                    }
                } catch (pollError) {
                    setError('Failed to get video status.');
                    setIsVideoLoading(false);
                }
            };

            poll();

        } catch (e) {
            console.error('Video generation failed:', e);
            setError(e instanceof Error ? e.message : "Video generation failed. Make sure the backend is running.");
            setIsVideoLoading(false);
        }
    }, [prompt]);

    const handleRemoveBackground = useCallback(async () => {
        if (selectedLayer?.type !== 'image' || !selectedLayer.image) {
            setError("Please select an image layer with an image.");
            return;
        }

        setIsProcessingImage(true);
        setError(null);
        try {
            const processedImage = await removeBackgroundFromImage(selectedLayer.image);
            updateLayer(selectedLayer.id, { image: processedImage });
        } catch (e) {
            setError(e instanceof Error ? e.message : "Failed to remove background.");
            console.error(e);
        } finally {
            setIsProcessingImage(false);
        }
    }, [selectedLayer]);

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            const reader = new FileReader();
            reader.readAsDataURL(event.target.files[0]);
            reader.onloadend = () => {
                if (selectedLayerId) {
                    updateLayer(selectedLayerId, { image: reader.result as string });
                } else {
                    const newLayer = createNewImageLayer('Uploaded Image');
                    newLayer.image = reader.result as string;
                    setLayers([newLayer]);
                    setSelectedLayerId(newLayer.id);
                }
            };
            reader.onerror = () => setError("Failed to read file.");
        }
    };

    const handleGenerateGraphic = (svgString: string) => {
        if (selectedLayerId) {
            const dataUrl = `data:image/svg+xml;base64,${btoa(svgString)}`;
            updateLayer(selectedLayerId, { image: dataUrl });
            setProjectMode('pattern');
        } else {
            setError("No layer selected to apply the graphic.");
        }
    };

    const handleCreateTextDesign = (config: TextDesignConfig) => {
        console.log("Creating text design with config:", config);
        const dummySvg = `<svg width="200" height="100" xmlns="http://www.w3.org/2000/svg"><text x="10" y="50" font-family="${config.fontFamily}" font-size="${config.fontSize}" fill="${config.color}">${config.text}</text></svg>`;
        const dataUrl = `data:image/svg+xml;base64,${btoa(dummySvg)}`;

        if (selectedLayerId) {
            updateLayer(selectedLayerId, { image: dataUrl });
            setProjectMode('pattern');
        } else {
            setError("No layer selected for the text design.");
        }
    };

    const handleDownload = () => {
        if (canvasRef.current) {
            // @ts-ignore - html2canvas types are incomplete
            html2canvas(canvasRef.current, {
                backgroundColor: null, 
                useCORS: true,
                scale: 2,
                allowTaint: true,
                logging: false
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = 'imogen-pattern.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            }).catch(err => {
                setError("Failed to capture pattern.");
                console.error("html2canvas error:", err);
            });
        }
    };

    const renderContent = () => {
        switch (projectMode) {
            case 'hub':
                return <HubScreen onStartProject={handleStartProject} />;
            case 'video':
                return <VideoWorkspace onBack={handleNewProject} />;
            case 'pattern':
                return (
                    <div className="relative">
                        <button 
                            onClick={handleNewProject}
                            className="absolute top-4 left-4 z-50 bg-white/80 hover:bg-white text-gray-700 font-medium py-2 px-4 rounded-lg shadow-md transition-colors"
                        >
                            ← Back to Hub
                        </button>
                        <PatternWorkspace />
                    </div>
                );
            case 'text':
                return <TextWorkspace onGenerate={handleCreateTextDesign} onBack={handleNewProject} />;
            case 'crest':
                return <GraphicDesignerWorkspace onGenerate={handleGenerateGraphic} onBack={handleNewProject} />;
            case 'pattern':
            case 'image':
                return (
                     <div className="relative flex flex-col md:flex-row min-h-screen p-4 md:p-6 gap-6">
                        <ControlPanel
                            projectMode={projectMode}
                            workflowStep={workflowStep}
                            prompt={prompt}
                            setPrompt={setPrompt}
                            isLoading={isLoading}
                            isProcessingImage={isProcessingImage}
                            handleGenerateImage={handleGenerateImage}
                            handleGenerateVideo={handleGenerateVideo}
                            isVideoLoading={isVideoLoading}
                            handleRemoveBackground={handleRemoveBackground}
                            handleImageUpload={handleImageUpload}
                            handleDownload={handleDownload}
                            handleNewProject={handleNewProject}
                            layers={layers}
                            selectedLayer={selectedLayer}
                            addImageLayer={addImageLayer}
                            addProceduralLayer={addProceduralLayer}
                            deleteLayer={deleteLayer}
                            selectLayer={setSelectedLayerId}
                            updateLayer={updateLayer}
                            globalRotation={globalRotation}
                            setGlobalRotation={setGlobalRotation}
                            zoom={zoom}
                            setZoom={setZoom}
                            background={background}
                            setBackground={setBackground}
                            error={error}
                        />
                        <main className="flex-1 flex items-center justify-center">
                            {videoUrl ? (
                                <div className="w-full max-w-2xl text-center">
                                    <video src={videoUrl} controls autoPlay className="w-full rounded-lg shadow-lg" />
                                    <div className="mt-4 space-x-4">
                                        <a
                                            href={videoUrl}
                                            download="generated_video.mp4"
                                            className="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded"
                                        >
                                            Download Video
                                        </a>
                                        <button
                                            onClick={() => setVideoUrl(null)}
                                            className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded"
                                        >
                                            Close
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <PatternCanvas
                                    ref={canvasRef}
                                    layers={layers}
                                    background={background}
                                    globalRotation={globalRotation}
                                    zoom={zoom}
                                />
                            )}
                        </main>
                    </div>
                );
            default:
                return <div>Unknown project mode. Please return to hub.</div>;
        }
    };

    return (
        <div className="relative min-h-screen w-full font-sans text-gray-800 bg-gradient-to-br from-sky-100 via-rose-100 to-amber-100">
            {renderContent()}
        </div>
    );
};

export default App;
