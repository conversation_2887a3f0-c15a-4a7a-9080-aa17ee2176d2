# Video Generation Backend

This directory contains a simple Python-based web server for generating videos using Google's Generative AI.

## Setup

1.  **Create a virtual environment (recommended):**
    ```bash
    python3 -m venv venv
    source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
    ```

2.  **Install the required packages:**
    ```bash
    pip install -r requirements.txt
    ```

3.  **Set up your Google API Key:**
    This server expects your Google API key to be available as an environment variable. You can set it in your terminal before running the server:
    ```bash
    export GOOGLE_API_KEY="YOUR_GOOGLE_API_KEY"
    ```
    *Note: Remember to replace `"YOUR_GOOGLE_API_KEY"` with your actual key.*

## Running the Server

Once the setup is complete, you can start the server with the following command:

```bash
python video_server.py
```

The server will start on `http://127.0.0.1:5000` and will be ready to accept requests from the frontend application.
