import time
import os
import json
from flask import Flask, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
import threading
import google.generativeai as genai
from google.generativeai.types import GenerateVideosConfig, Image as GenAIImage
from rembg import remove
from PIL import Image as PILImage
import io

# --- Configuration ---
# It's recommended to use environment variables for these.
PROJECT_ID = "imogen-ai-studio"
LOCATION = "us-central1"

# Configure the google-genai SDK to use the Vertex AI backend
genai.configure(
    transport='vertex_ai',
    client_options={
        "project": PROJECT_ID,
        "location": LOCATION,
    }
)

app = Flask(__name__)
CORS(app)

video_jobs = {}

def generate_video_task(prompt, job_id, image_bytes=None, params=None):
    """
    This function runs the video generation in a separate thread using the Vertex AI backend.
    It now handles long-running operations and detailed parameters.
    """
    if params is None:
        params = {}
    video_jobs[job_id] = {"status": "processing", "prompt": prompt}
    try:
        print(f"[{job_id}] Starting Vertex AI video generation for prompt: '{prompt}'")

        # Build the configuration for video generation from the provided parameters
        config = GenerateVideosConfig(
            duration_seconds=int(params.get("durationSeconds", 8)),
            aspect_ratio=params.get("aspectRatio", "16:9"),
            generate_audio=params.get("generateAudio", True),
            resolution=params.get("resolution", "720p"),
            person_generation=params.get("personGeneration", "allow_all"),
            number_of_videos=int(params.get("sampleCount", 1)),
        )

        # Prepare the reference image if provided
        reference_image = None
        if image_bytes:
            reference_image = GenAIImage(image_bytes=image_bytes)

        # Start the long-running video generation operation
        operation = genai.models.generate_videos(
            model="models/veo-3.0-generate-001",  # Using the model from your snippet
            prompt=prompt,
            image=reference_image,
            config=config,
        )

        print(f"[{job_id}] Started operation: {operation.name}")
        video_jobs[job_id]['status'] = 'running'
        video_jobs[job_id]['operation_name'] = operation.name

        # --- File Handling ---
        output_dir = "videos"
        os.makedirs(output_dir, exist_ok=True)
        video_filename = f"{job_id}.mp4"
        video_path = os.path.join(output_dir, video_filename)

        # Poll for completion
        while not operation.done:
            time.sleep(10)  # Wait for 10 seconds before checking again
            operation.refresh() # Refresh the operation state
            print(f"[{job_id}] Checking operation status: {'done' if operation.done else 'running'}")

        if operation.error:
            raise Exception(str(operation.error))

        result = operation.result
        generated_videos = result.generated_videos

        if not generated_videos:
            raise ValueError("Video generation completed but returned no videos.")

        video_part = generated_videos[0].video
        # Save the video file from the response bytes
        with open(video_path, "wb") as f:
            f.write(video_part.video_bytes)

        print(f"[{job_id}] Video successfully saved to: {video_path}")
        video_url = f"/videos/{video_filename}"
        video_jobs[job_id] = {"status": "completed", "video_url": video_url}

    except Exception as e:
        print(f"[{job_id}] An error occurred during video generation: {e}")
        video_jobs[job_id] = {"status": "failed", "error": str(e)}


# --- API Endpoints ---

@app.route('/remove-background', methods=['POST'])
def remove_background_endpoint():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part in the request'}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    if file:
        input_image = PILImage.open(file.stream)
        output_image = remove(input_image)

        img_io = io.BytesIO()
        output_image.save(img_io, 'PNG')
        img_io.seek(0)

        return send_file(img_io, mimetype='image/png')

@app.route('/generate-video', methods=['POST'])
def generate_video_endpoint():
    # Using multipart/form-data to allow for optional image upload
    if 'prompt' not in request.form:
        return jsonify({'error': 'A "prompt" form field is required.'}), 400

    prompt = request.form['prompt']
    # Parameters are sent as a JSON string in a form field
    params_str = request.form.get('params', '{}')
    try:
        params = json.loads(params_str)
    except json.JSONDecodeError:
        return jsonify({'error': 'The "params" field contains invalid JSON.'}), 400

    image_bytes = None
    if 'image' in request.files:
        image_file = request.files['image']
        if image_file.filename != '':
            image_bytes = image_file.read()

    job_id = f"job_{int(time.time())}"
    # Start the background task
    thread = threading.Thread(target=generate_video_task, args=(prompt, job_id, image_bytes, params))
    thread.start()

    return jsonify({
        'message': 'Video generation process started.',
        'job_id': job_id,
        'status_url': f'/video-status/{job_id}'
    }), 202

@app.route('/video-status/<job_id>', methods=['GET'])
def get_video_status(job_id):
    job = video_jobs.get(job_id)
    if not job:
        return jsonify({'error': 'Job not found.'}), 404
    return jsonify(job)

@app.route('/videos/<filename>')
def serve_video(filename):
    return send_from_directory('videos', filename)


if __name__ == '__main__':
    app.run(debug=True, port=5000)
